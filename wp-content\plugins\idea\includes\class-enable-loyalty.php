<?php

class EnableLoyalty {
    private $apiKey;
    private $debugMode;
    private $testMode;
    private $enableHelper;
    private $isLoyaltyEnabled;


    public function __construct() {
        $this->apiKey = get_option('enable_order_api_key');
        $this->debugMode = get_option('enable_order_debug_mode') === '1';
        $this->testMode = get_option('enable_order_test_mode') === '1';
        $this->isLoyaltyEnabled = get_option('enable_order_enable_loyalty') === '1';
        $this->enableHelper = new EnableHelper();

        if ($this->isLoyaltyEnabled) {
            $this->init();
        }
    }

    private function init() {
        add_action('wp_enqueue_scripts', [$this, 'enqueueAssets']);

        add_action('woocommerce_cart_calculate_fees', [$this, 'addingEnableLoyaltyDiscountFees'], 10, 1);


        add_action('woocommerce_before_checkout_form', [$this, 'displayLoyaltyRegistrationLink']);
        add_action('woocommerce_checkout_after_order_review', [$this, 'displayLoyaltyRegistrationLink']);
        // AJAX actions
        //  check Loyalty
        add_action('wp_ajax_check_loyalty', [$this, 'checkLoyalty']);
        add_action('wp_ajax_nopriv_check_loyalty', [$this, 'checkLoyalty']);
        // ApplyTier Discount Ajax
        add_action('wp_ajax_apply_tier_discount', [$this, 'applyTierDiscount']);
        add_action('wp_ajax_nopriv_apply_tier_discount', [$this, 'applyTierDiscount']);

        // ApplyCoupon Ajax
        add_action('wp_ajax_apply_coupon', [$this, 'applyCoupon']);
        add_action('wp_ajax_nopriv_apply_coupon', [$this, 'applyCoupon']);

        // Add new action for removing coupon
        add_action('wp_ajax_remove_coupon', [$this, 'removeCoupon']);
        add_action('wp_ajax_nopriv_remove_coupon', [$this, 'removeCoupon']);
    }

    public function displayLoyaltyRegistrationLink() {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        $registrationUrl = get_option('enable_order_loyalty_program_registration_url');
        $registrationText = get_option('enable_order_loyalty_program_registration_text');
        if ($registrationUrl && $registrationText) {
            echo '<div class="enable-loyalty-registration">';
            echo '<a href="' . esc_url($registrationUrl) . '" target="_blank">' . esc_html($registrationText) . '</a>';
            echo '</div>';
        }
    }

    public function checkLoyalty() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $phone = sanitize_text_field($_POST['phone']);
        $countryCode = sanitize_text_field($_POST['countryCode']);
        wc_get_logger()->info('Phone: ' . $phone, ['source' => 'enable_loyalty']);
        wc_get_logger()->info('Country Code: ' . $countryCode, ['source' => 'enable_loyalty']);
        $customerDetails = $this->enableHelper->lookupCustomerDetails($phone, '+'.$countryCode, $this->apiKey, $this->testMode);
        WC()->session->set('enable_loyalty_discount', array());
        wp_send_json($customerDetails);
    }

    public function enqueueAssets() {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        // Enqueue scripts
        wp_enqueue_script(
            'intlTelInput',
            plugins_url('assets/js/intlTelInput.min.js', __FILE__),
            array('jquery')
        );

        wp_enqueue_script(
            'main-script',
            plugins_url('assets/js/main.js', __FILE__),
            array('jquery'),
        );

        // Localize script
        wp_localize_script('main-script', 'my_script_params', array(
            'countryCode' => '+974',
            'AJAX_URL' => admin_url('admin-ajax.php')
        ));

        // Enqueue styles
        wp_enqueue_style('main-style', plugins_url('/assets/css/main.css', __FILE__));
        wp_enqueue_style('intlTelInput-style', plugins_url('/assets/css/intlTelInput.min.css', __FILE__));
    }

    public function applyTierDiscount() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $phone = sanitize_text_field($_POST['phone']);
        $countryCode = sanitize_text_field($_POST['countryCode']);
        $customerTier = $_POST['customerTier'];

        // Get the tier discount from the customerTier object
        $tierDiscount = isset($customerTier['tierDiscount']) ? floatval($customerTier['tierDiscount']) : 0;
        wc_get_logger()->info('Tier Discount: ' . $tierDiscount, ['source' => 'enable_loyalty']);

        if ($tierDiscount > 0) {
            // Store the discount data in the session
            $existingDiscount = WC()->session->get('enable_loyalty_discount');
            if ($existingDiscount) {
                $existingDiscount['tier_discount'] = $tierDiscount;
            } else {
                $existingDiscount = array(
                    'tier_discount' => $tierDiscount,
                );
            }
            WC()->session->set('enable_loyalty_discount', $existingDiscount);


            wp_send_json_success([
                'message' => sprintf(__('Applied %.2f%% tier discount', 'enable-loyalty'), $tierDiscount * 100),
            ]);
        } else {
            wp_send_json_error([
                'message' => __('No tier discount available', 'enable-loyalty')
            ]);
        }
    }

    public function applyCoupon() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $couponId = sanitize_text_field($_POST['couponId']);
        $couponValue = sanitize_text_field($_POST['couponValue']);

        // Remove any existing coupon
        $existingDiscount = WC()->session->get('enable_loyalty_discount');
        if (isset($existingDiscount['coupon_discount'])) {
            unset($existingDiscount['coupon_discount']);
            unset($existingDiscount['couponId']);
        }

        // Add the new coupon
        $couponAmount = floatval($couponValue);
        if ($couponAmount > 0) {
            $existingDiscount['coupon_discount'] = $couponAmount;
            $existingDiscount['couponId'] = $couponId;
            WC()->session->set('enable_loyalty_discount', $existingDiscount);

            wp_send_json_success([
                'message' => sprintf(__('Applied coupon %s with value %.2f', 'enable-loyalty'), $couponId, $couponAmount),
            ]);
        } else {
            wp_send_json_error([
                'message' => __('Invalid coupon value', 'enable-loyalty')
            ]);
        }
    }

    public function addingEnableLoyaltyDiscountFees($cart) {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        $discount_data = WC()->session->get('enable_loyalty_discount');

        if (isset($discount_data['tier_discount'])) {
            $tierDiscount = floatval($discount_data['tier_discount']);
            if ($tierDiscount > 0) {
                $subtotal = $cart->get_subtotal();
                $this->removeTierDiscount($cart);

                // Use cart subtotal excluding taxes
                $discountAmount = $subtotal * $tierDiscount;

                // Apply the discount as a negative fee
                $cart->add_fee(__('Tier Discount', 'enable-loyalty'), -$discountAmount);

                // Log for debugging
                wc_get_logger()->info('Applied Tier Discount Fee: ' . $discountAmount, ['source' => 'enable_loyalty']);
            }
        } else {
            $this->removeTierDiscount($cart);
            wc_get_logger()->info('Tier Discount not applied', ['source' => 'enable_loyalty']);
        }


        if (isset($discount_data['coupon_discount'])) {
            $couponDiscount = floatval($discount_data['coupon_discount']);
            if ($couponDiscount > 0) {
                $subtotal = $cart->get_subtotal();

                $this->removeCouponDiscount($cart);

                // Apply the discount as a negative fee with extra information
                $cart->add_fee(__('Coupon Discount', 'enable-loyalty'), -$couponDiscount, false);

                // Log for debugging
                wc_get_logger()->info('Applied Coupon Discount Fee: ' . $couponDiscount, ['source' => 'enable_loyalty']);
            }
        } else {
            $this->removeCouponDiscount($cart);
            wc_get_logger()->info('Coupon Discount not applied', ['source' => 'enable_loyalty']);
        }
    }

    public function removeCouponDiscount($cart) {
        foreach ($cart->get_fees() as $key => $fee) {
            if ($fee->name === __('Coupon Discount', 'enable-loyalty')) {
                unset($cart->fees[$key]);
            }
        }
    }

    public function removeTierDiscount($cart) {
        foreach ($cart->get_fees() as $key => $fee) {
            if ($fee->name === __('Tier Discount', 'enable-loyalty')) {
                unset($cart->fees[$key]);
            }
        }
    }

    public function removeCoupon() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $couponId = sanitize_text_field($_POST['couponId']);

        $existingDiscount = WC()->session->get('enable_loyalty_discount');
        if ($existingDiscount && isset($existingDiscount['coupon_discount']) && isset($existingDiscount['couponId'])) {
            if ($existingDiscount['couponId'] === $couponId) {
                unset($existingDiscount['coupon_discount']);
                unset($existingDiscount['couponId']);
                WC()->session->set('enable_loyalty_discount', $existingDiscount);

                $cart = WC()->cart;
                $this->removeCouponDiscount($cart);

                wp_send_json_success([
                    'message' => sprintf(__('Removed coupon %s', 'enable-loyalty'), $couponId),
                ]);
            } else {
                wp_send_json_error([
                    'message' => __('This coupon is not currently applied', 'enable-loyalty')
                ]);
            }
        } else {
            wp_send_json_error([
                'message' => __('No coupon is currently applied', 'enable-loyalty')
            ]);
        }
    }

}
