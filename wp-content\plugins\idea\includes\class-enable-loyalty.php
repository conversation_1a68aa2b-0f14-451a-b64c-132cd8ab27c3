<?php

class EnableLoyalty {
    private $apiKey;
    private $debugMode;
    private $testMode;
    private $enableHelper;
    private $isLoyaltyEnabled;


    public function __construct() {
        $this->apiKey = get_option('enable_order_api_key');
        $this->debugMode = get_option('enable_order_debug_mode') === '1';
        $this->testMode = get_option('enable_order_test_mode') === '1';
        $this->isLoyaltyEnabled = get_option('enable_order_enable_loyalty') === '1';
        $this->enableHelper = new EnableHelper();

        if ($this->isLoyaltyEnabled) {
            $this->init();
        }
    }

    private function init() {
        add_action('wp_enqueue_scripts', [$this, 'enqueueAssets']);

        add_action('woocommerce_cart_calculate_fees', [$this, 'addingEnableLoyaltyDiscountFees'], 10, 1);


        // Add loyalty registration link to various checkout locations
        add_action('woocommerce_before_checkout_form', [$this, 'displayLoyaltyRegistrationLink']);
        add_action('woocommerce_checkout_after_order_review', [$this, 'displayLoyaltyRegistrationLink']);

        // For block-based checkout
        add_action('woocommerce_blocks_checkout_block_registration', [$this, 'register_checkout_block_integration']);

        // Additional hooks for better compatibility
        add_action('woocommerce_checkout_before_customer_details', [$this, 'displayLoyaltyRegistrationLink']);
        add_action('woocommerce_checkout_billing', [$this, 'displayLoyaltyRegistrationLink']);
        // AJAX actions
        //  check Loyalty
        add_action('wp_ajax_check_loyalty', [$this, 'checkLoyalty']);
        add_action('wp_ajax_nopriv_check_loyalty', [$this, 'checkLoyalty']);
        // ApplyTier Discount Ajax
        add_action('wp_ajax_apply_tier_discount', [$this, 'applyTierDiscount']);
        add_action('wp_ajax_nopriv_apply_tier_discount', [$this, 'applyTierDiscount']);

        // ApplyCoupon Ajax
        add_action('wp_ajax_apply_coupon', [$this, 'applyCoupon']);
        add_action('wp_ajax_nopriv_apply_coupon', [$this, 'applyCoupon']);

        // Add new action for removing coupon
        add_action('wp_ajax_remove_coupon', [$this, 'removeCoupon']);
        add_action('wp_ajax_nopriv_remove_coupon', [$this, 'removeCoupon']);
    }

    public function displayLoyaltyRegistrationLink() {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        $registrationUrl = get_option('enable_order_loyalty_program_registration_url');
        $registrationText = get_option('enable_order_loyalty_program_registration_text');
        if ($registrationUrl && $registrationText) {
            echo '<div class="enable-loyalty-registration">';
            echo '<a href="' . esc_url($registrationUrl) . '" target="_blank">' . esc_html($registrationText) . '</a>';
            echo '</div>';
        }
    }

    /**
     * Register checkout block integration for loyalty features
     */
    public function register_checkout_block_integration() {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        // This would be expanded to include block-specific integration
        // For now, we rely on the JavaScript to handle both classic and block checkout
    }

    public function checkLoyalty() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'enable_loyalty_nonce')) {
            wp_send_json_error([
                'message' => __('Security check failed', 'enable-loyalty')
            ]);
            return;
        }

        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $phone = sanitize_text_field($_POST['phone'] ?? '');
        $countryCode = sanitize_text_field($_POST['countryCode'] ?? '');

        if (empty($phone)) {
            wp_send_json_error([
                'message' => __('Phone number is required', 'enable-loyalty')
            ]);
            return;
        }

        if ($this->debugMode) {
            wc_get_logger()->info('Phone: ' . $phone, ['source' => 'enable_loyalty']);
            wc_get_logger()->info('Country Code: ' . $countryCode, ['source' => 'enable_loyalty']);
        }

        $customerDetails = $this->enableHelper->lookupCustomerDetails($phone, '+'.$countryCode, $this->apiKey, $this->testMode);
        WC()->session->set('enable_loyalty_discount', array());
        wp_send_json($customerDetails);
    }

    public function enqueueAssets() {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        // Only enqueue on checkout page
        if (!is_checkout()) {
            return;
        }

        // Enqueue scripts with proper dependencies for WooCommerce
        wp_enqueue_script(
            'intlTelInput',
            plugins_url('assets/js/intlTelInput.min.js', __FILE__),
            array('jquery'),
            '17.0.8',
            true // Load in footer
        );

        // Determine dependencies based on WooCommerce version and checkout type
        $dependencies = array('jquery', 'intlTelInput');

        // Add WooCommerce checkout dependencies if available
        if (wp_script_is('wc-checkout', 'registered')) {
            $dependencies[] = 'wc-checkout';
        }
        if (wp_script_is('wc-blocks-checkout', 'registered')) {
            $dependencies[] = 'wc-blocks-checkout';
        }

        wp_enqueue_script(
            'enable-main-script',
            plugins_url('assets/js/main.js', __FILE__),
            $dependencies,
            '1.14.1', // Version number
            true // Load in footer
        );

        // Localize script with additional data
        wp_localize_script('enable-main-script', 'my_script_params', array(
            'countryCode' => '+974',
            'AJAX_URL' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('enable_loyalty_nonce'),
            'debug' => $this->debugMode
        ));

        // Enqueue styles
        wp_enqueue_style(
            'enable-main-style',
            plugins_url('/assets/css/main.css', __FILE__),
            array(),
            '1.14.1'
        );
        wp_enqueue_style(
            'enable-intlTelInput-style',
            plugins_url('/assets/css/intlTelInput.min.css', __FILE__),
            array(),
            '17.0.8'
        );
    }

    public function applyTierDiscount() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'enable_loyalty_nonce')) {
            wp_send_json_error([
                'message' => __('Security check failed', 'enable-loyalty')
            ]);
            return;
        }

        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $phone = sanitize_text_field($_POST['phone'] ?? '');
        $countryCode = sanitize_text_field($_POST['countryCode'] ?? '');
        $customerTier = $_POST['customerTier'] ?? null;

        if (!$customerTier) {
            wp_send_json_error([
                'message' => __('Invalid tier data', 'enable-loyalty')
            ]);
            return;
        }

        // Get the tier discount from the customerTier object
        $tierDiscount = isset($customerTier['tierDiscount']) ? floatval($customerTier['tierDiscount']) : 0;

        if ($this->debugMode) {
            wc_get_logger()->info('Tier Discount: ' . $tierDiscount, ['source' => 'enable_loyalty']);
        }

        if ($tierDiscount > 0) {
            // Store the discount data in the session
            $existingDiscount = WC()->session->get('enable_loyalty_discount');
            if ($existingDiscount) {
                $existingDiscount['tier_discount'] = $tierDiscount;
            } else {
                $existingDiscount = array(
                    'tier_discount' => $tierDiscount,
                );
            }
            WC()->session->set('enable_loyalty_discount', $existingDiscount);

            wp_send_json_success([
                'message' => sprintf(__('Applied %.2f%% tier discount', 'enable-loyalty'), $tierDiscount * 100),
            ]);
        } else {
            wp_send_json_error([
                'message' => __('No tier discount available', 'enable-loyalty')
            ]);
        }
    }

    public function applyCoupon() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $couponId = sanitize_text_field($_POST['couponId']);
        $couponValue = sanitize_text_field($_POST['couponValue']);

        // Remove any existing coupon
        $existingDiscount = WC()->session->get('enable_loyalty_discount');
        if (isset($existingDiscount['coupon_discount'])) {
            unset($existingDiscount['coupon_discount']);
            unset($existingDiscount['couponId']);
        }

        // Add the new coupon
        $couponAmount = floatval($couponValue);
        if ($couponAmount > 0) {
            $existingDiscount['coupon_discount'] = $couponAmount;
            $existingDiscount['couponId'] = $couponId;
            WC()->session->set('enable_loyalty_discount', $existingDiscount);

            wp_send_json_success([
                'message' => sprintf(__('Applied coupon %s with value %.2f', 'enable-loyalty'), $couponId, $couponAmount),
            ]);
        } else {
            wp_send_json_error([
                'message' => __('Invalid coupon value', 'enable-loyalty')
            ]);
        }
    }

    public function addingEnableLoyaltyDiscountFees($cart) {
        if (!$this->isLoyaltyEnabled) {
            return;
        }

        $discount_data = WC()->session->get('enable_loyalty_discount');

        if (isset($discount_data['tier_discount'])) {
            $tierDiscount = floatval($discount_data['tier_discount']);
            if ($tierDiscount > 0) {
                $subtotal = $cart->get_subtotal();
                $this->removeTierDiscount($cart);

                // Use cart subtotal excluding taxes
                $discountAmount = $subtotal * $tierDiscount;

                // Apply the discount as a negative fee
                $cart->add_fee(__('Tier Discount', 'enable-loyalty'), -$discountAmount);

                // Log for debugging
                wc_get_logger()->info('Applied Tier Discount Fee: ' . $discountAmount, ['source' => 'enable_loyalty']);
            }
        } else {
            $this->removeTierDiscount($cart);
            wc_get_logger()->info('Tier Discount not applied', ['source' => 'enable_loyalty']);
        }


        if (isset($discount_data['coupon_discount'])) {
            $couponDiscount = floatval($discount_data['coupon_discount']);
            if ($couponDiscount > 0) {
                $subtotal = $cart->get_subtotal();

                $this->removeCouponDiscount($cart);

                // Apply the discount as a negative fee with extra information
                $cart->add_fee(__('Coupon Discount', 'enable-loyalty'), -$couponDiscount, false);

                // Log for debugging
                wc_get_logger()->info('Applied Coupon Discount Fee: ' . $couponDiscount, ['source' => 'enable_loyalty']);
            }
        } else {
            $this->removeCouponDiscount($cart);
            wc_get_logger()->info('Coupon Discount not applied', ['source' => 'enable_loyalty']);
        }
    }

    public function removeCouponDiscount($cart) {
        foreach ($cart->get_fees() as $key => $fee) {
            if ($fee->name === __('Coupon Discount', 'enable-loyalty')) {
                unset($cart->fees[$key]);
            }
        }
    }

    public function removeTierDiscount($cart) {
        foreach ($cart->get_fees() as $key => $fee) {
            if ($fee->name === __('Tier Discount', 'enable-loyalty')) {
                unset($cart->fees[$key]);
            }
        }
    }

    public function removeCoupon() {
        if (!$this->isLoyaltyEnabled) {
            wp_send_json_error([
                'message' => __('Loyalty program is not enabled', 'enable-loyalty')
            ]);
            return;
        }

        $couponId = sanitize_text_field($_POST['couponId']);

        $existingDiscount = WC()->session->get('enable_loyalty_discount');
        if ($existingDiscount && isset($existingDiscount['coupon_discount']) && isset($existingDiscount['couponId'])) {
            if ($existingDiscount['couponId'] === $couponId) {
                unset($existingDiscount['coupon_discount']);
                unset($existingDiscount['couponId']);
                WC()->session->set('enable_loyalty_discount', $existingDiscount);

                $cart = WC()->cart;
                $this->removeCouponDiscount($cart);

                wp_send_json_success([
                    'message' => sprintf(__('Removed coupon %s', 'enable-loyalty'), $couponId),
                ]);
            } else {
                wp_send_json_error([
                    'message' => __('This coupon is not currently applied', 'enable-loyalty')
                ]);
            }
        } else {
            wp_send_json_error([
                'message' => __('No coupon is currently applied', 'enable-loyalty')
            ]);
        }
    }

}
