<?php

class EnableOrder {
    private string $apiKey;
    private EnableHelper $enableHelper;
    private bool $debugMode;
    private bool $testMode;

    public function __construct() {
        $this->enableHelper = new EnableHelper();
        $this->registerHooks();
    }

    private function registerHooks(): void {
        add_action('admin_menu', [$this, 'addAdminMenu']);
        add_action('admin_init', [$this, 'registerSettings']);
        add_action('woocommerce_payment_complete', [$this, 'onPaymentCompleted']);
        add_action('woocommerce_order_status_changed', [$this, 'onOrderStatusChanged'], 10, 3);
        // Add new hooks for sync button
        add_action('woocommerce_order_actions', [$this, 'addOrderActions']);
        add_action('woocommerce_order_action_enable_sync_order', [$this, 'handleSyncAction']);
    
        add_action('admin_notices', [$this, 'showOrderSyncNotice']);

    }

 

    public function addAdminMenu(): void {
        add_options_page(
            'Enable Settings',
            'Enable Order & Loyalty Program',
            'manage_options',
            'enable-order',
            [$this, 'renderSettingsPage']
        );
    }

    public function registerSettings(): void {
        $settings = ['api_key', 'debug_mode', 'test_mode', 'delivery_type', 'enable_loyalty', 'loyalty_program_registration_url', 'loyalty_program_registration_text'];
        foreach ($settings as $setting) {
            register_setting('enable_order_settings', "enable_order_$setting");
        }
    }

    public function renderSettingsPage(): void {
        ?>
        <div class="wrap">
            <h1>Enable-Order Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('enable_order_settings');
                do_settings_sections('enable_order_settings');
                $this->renderSettingsFields();
                submit_button('Save Settings');
                ?>
            </form>
        </div>
        <?php
        $this->enqueueSettingsStyles();
    }

    private function renderSettingsFields(): void {
        ?>
        <table class="form-table">
            <?php $this->renderTextField('api_key', 'API Key', 'Enter your API key here. You can find this in your account settings.'); ?>
            <?php $this->renderCheckboxField('debug_mode', 'Debug Mode', 'Enable Debug Mode', 'Check this box to enable debug mode. No real transactions will be processed.'); ?>
            <?php $this->renderCheckboxField('test_mode', 'Test Mode', 'Enable Test Mode', 'Check this box to enable test mode. A transaction will be sent to Staging ENV.'); ?>
            <?php $this->renderSelectField('delivery_type', 'Delivery Type', ['urgent' => 'Urgent', 'scheduled' => 'Scheduled'], 'Choose the default delivery type for orders.'); ?>
        </table>

        <h2>Loyalty Program Settings</h2>
        <table class="form-table">
            <?php $this->renderCheckboxField('enable_loyalty', 'Enable Loyalty Program', 'Enable Loyalty Features', 'Check this box to enable loyalty program features on your site.'); ?>
            <?php $this->renderTextField('loyalty_program_registration_url', 'Loyalty Program Registration URL', 'Enter the URL for loyalty program registration.'); ?>
            <?php $this->renderTextField('loyalty_program_registration_text', 'Loyalty Program Registration Text', 'Enter the text to display for loyalty program registration.'); ?>
        </table>
        <?php
    }

    private function renderSelectField(string $name, string $label, array $options, string $description): void {
        $id = "enable_order_$name";
        $value = esc_attr(get_option($id));
        ?>
        <tr valign="top">
            <th scope="row"><label for="<?php echo $id; ?>"><?php echo $label; ?></label></th>
            <td>
                <select id="<?php echo $id; ?>" name="<?php echo $id; ?>">
                    <?php foreach ($options as $key => $option): ?>
                        <option value="<?php echo $key; ?>" <?php selected($value, $key); ?>><?php echo $option; ?></option>
                    <?php endforeach; ?>
                </select>
                <p class="description"><?php echo $description; ?></p>
            </td>
        </tr>
        <?php
    }


    private function renderTextField(string $name, string $label, string $description): void {
        $id = "enable_order_$name";
        $value = esc_attr(get_option($id));
        ?>
        <tr valign="top">
            <th scope="row"><label for="<?php echo $id; ?>"><?php echo $label; ?></label></th>
            <td>
                <input type="text" id="<?php echo $id; ?>" name="<?php echo $id; ?>" value="<?php echo $value; ?>" class="regular-text" />
                <p class="description"><?php echo $description; ?></p>
            </td>
        </tr>
        <?php
    }

    private function renderCheckboxField(string $name, string $label, string $checkboxLabel, string $description): void {
        $id = "enable_order_$name";
        $checked = checked(get_option($id), '1', false);
        ?>
        <tr valign="top">
            <th scope="row"><label for="<?php echo $id; ?>"><?php echo $label; ?></label></th>
            <td>
                <input type="checkbox" id="<?php echo $id; ?>" name="<?php echo $id; ?>" value="1" <?php echo $checked; ?> />
                <label for="<?php echo $id; ?>"><?php echo $checkboxLabel; ?></label>
                <p class="description"><?php echo $description; ?></p>
            </td>
        </tr>
        <?php
    }

    private function enqueueSettingsStyles(): void {
        ?>
        <style>
            .form-table th { padding-top: 20px; }
            .form-table td { padding-top: 15px; }
            .form-table input[type="text"],
            .form-table select { width: 25em; max-width: 100%; }
            .description { font-style: italic; color: #666; margin-top: 5px; }
        </style>
        <?php
    }

    public function onPaymentCompleted($order_id): void {
        error_log('Enable: onPaymentCompleted called' . $order_id);
        $this->processOrder($order_id);
    }

    public function onOrderStatusChanged($order_id, $old_status, $new_status): void {
        error_log('Enable: onOrderStatusChanged called' . $order_id);
        $this->processOrder($order_id);
    }

    public function addOrderActions($actions) {
        $actions['enable_sync_order'] = __('Sync to Enable');
        return $actions;
    }

    public function handleSyncAction($order) {
    if (!$order instanceof WC_Order) {
        error_log('Enable: Order is not an instance of WC_Order');
        return;
    }

    $alreadySynced = $order->get_meta('EBL_enableOrderSynced');

if (!empty($alreadySynced)) {
    $orderCode = $order->get_meta('EBL_enableOrderCode') ?: 'N/A';
    set_transient('enable_order_notice_' . get_current_user_id(), [
        'type' => 'warning',
        'message' => "This order has already been synced to Enable. (Code: {$orderCode}) Re-sync is not allowed."
    ], 30);
    return;
}

    try {
        $this->performAction($order);

        set_transient('enable_order_notice_' . get_current_user_id(), [
            'type' => 'success',
            'message' => 'Order synced with Enable successfully.'
        ], 30);
    } catch (Exception $e) {
        error_log('Enable: Sync failed - ' . $e->getMessage());
        set_transient('enable_order_notice_' . get_current_user_id(), [
            'type' => 'error',
            'message' => 'Sync failed: ' . $e->getMessage()
        ], 30);
    }
}

public function showOrderSyncNotice() {
    $notice = get_transient('enable_order_notice_' . get_current_user_id());
    if (!$notice) return;

    delete_transient('enable_order_notice_' . get_current_user_id());

    $type = $notice['type'];
    $message = esc_html($notice['message']);

    echo "<div class='notice notice-{$type} is-dismissible'><p><strong>Enable:</strong> {$message}</p></div>";
}



    private function processOrder($order_id): void {
        $order = wc_get_order($order_id);
        $enableOrderCode = $order->get_meta('EBL_enableOrderCode');

        if ($order->is_paid() && empty($enableOrderCode)) {
            error_log('Enable: Order is paid' . $order_id);
            $this->performAction($order);
        }
    }

 private function performAction(WC_Order $order): void {
    try {
        // Avoid syncing twice
        $alreadySynced = $order->get_meta('EBL_enableOrderSynced');
        if (!empty($alreadySynced)) {
            error_log("Enable: Order #{$order->get_id()} already synced. Skipping.");
            return;
        }

        $this->apiKey = get_option('enable_order_api_key');
        $debugMode = get_option('enable_order_debug_mode') === '1';
        $testMode = '1' === get_option('enable_order_test_mode');
        $deliveryType = get_option('enable_order_delivery_type') ?: 'urgent';

        $requestDetails = [
            'integrationType' => 'order',
            'orderData' => $this->enableHelper->extractOrderData($order, $debugMode, $deliveryType),
        ];

        $response = $this->enableHelper->performIntegrationRequest(
            $requestDetails, $order, $this->apiKey, true, $debugMode, $testMode
        );

        if (is_wp_error($response)) {
            error_log('Enable: Error: ' . $response->get_error_message());
            $order->update_meta_data('EBL_enableOrderCode', 'Error');
        } elseif (wp_remote_retrieve_response_code($response) !== 200) {
            error_log('Enable: Error: ' . wp_remote_retrieve_response_message($response));
            $order->update_meta_data('EBL_enableOrderCode', 'Error');
        } else {
            $this->processSuccessfulResponse($order, $response);
        }
    } catch (Exception $e) {
        $order->update_meta_data('EBL_enableOrderCode', 'Error');
        $order->update_meta_data('EBL_enableOrderError', $e->getMessage());
    }
}

private function processSuccessfulResponse(WC_Order $order, array $response): void {
    $responseBody = wp_remote_retrieve_body($response);
    $responseJson = json_decode($responseBody);

    $order->update_meta_data('EBL_enableOrderCode', $responseJson->data->code);
    $order->update_meta_data('EBL_enableOrderSynced', true);
    $order->save();
}

}
