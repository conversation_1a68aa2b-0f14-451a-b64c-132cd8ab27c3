# Enable WooCommerce Integration Plugin

## Overview

Enable WooCommerce Integration is a comprehensive plugin designed for WooCommerce stores to seamlessly integrate with the Enable platform, providing enhanced features for order management, payment processing, and loyalty program integration.

## Features

### 1. Order Management Integration
- Sends order data to the Enable platform when orders are paid or when status changes.
- Configurable delivery types (urgent/scheduled) via settings.
- Manual order sync option through WooCommerce admin interface.
- Tracks integration status with custom order metadata.

### 2. Payment Gateway Integration
- Adds Enable as a payment gateway option within WooCommerce.
- Supports both live and test mode transactions.
- Easily configurable through WordPress admin settings.

### 3. Loyalty Program Integration
- Full loyalty program functionality for customers.
- Customer lookup by phone number with support for country codes.
- Tier-based discount system offering percentage-based savings.
- Coupon system integration with AJAX-powered updates.
- Loyalty program registration link available on checkout pages.

### 4. Administrative Features
- Complete settings page within WordPress admin.
- Includes debug and test modes for development and staging environments.
- Configurable API key for secure integration.
- User-friendly loyalty program settings with custom links and text.

## Technical Information
- Developed using modern PHP practices, including class structure and type hints.
- Utilizes WordPress/WooCommerce hooks and filters for seamless integration.
- Includes robust error logging and exception handling capabilities.
- Session-based discount tracking for efficient management.
- AJAX integration for improved user experience.
- Responsive admin interface styling for an intuitive setup.

## Authors
- Yousef Rabie
- Asmar

## Version

Current Version: 1.13
