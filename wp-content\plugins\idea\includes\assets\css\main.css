.enable-loyalty-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enable-loyalty-display h3 {
  color: #343a40;
  font-size: 1.2em;
  margin-bottom: 10px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.enable-loyalty-display h4 {
  color: #495057;
  font-size: 1em;
  margin-top: 15px;
  margin-bottom: 8px;
}

.loyalty-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.loyalty-info p {
  background-color: #e9ecef;
  padding: 8px;
  border-radius: 5px;
  margin-bottom: 5px;
  text-align: left;
  font-size: 0.9em;
}

.coupon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-bottom: 5px;
}

.enable-coupon-btn {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  width: calc(50% - 4px);
  height: auto;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 8px;
  font-size: 0.9em;
}

.enable-coupon-btn:hover {
  background-color: #0056b3;
}

.enable-coupon-btn.active {
  background-color: #28a745; /* Change to green when active */
}

.enable-coupon-btn .coupon-value {
  font-weight: bold;
  margin-bottom: 5px;
}

.enable-coupon-btn .coupon-cost {
  font-size: 0.8em;
  opacity: 0.9;
}

.enable-coupon-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

@media (max-width: 768px) {
  .enable-coupon-btn {
    width: 100%;
  }
}

/* Enhanced Phone Number Field Styling */
#billing-phone, #billing_phone {
  width: 100% !important;
  padding: 12px 15px !important;
  border: 2px solid #e1e5e9 !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
  background-color: #ffffff !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  outline: none !important;
}

#billing-phone:focus, #billing_phone:focus {
  border-color: #007bff !important;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
  background-color: #ffffff !important;
}

#billing-phone:hover, #billing_phone:hover {
  border-color: #007bff !important;
}

/* International Telephone Input Styling */
.iti {
  width: 100% !important;
}

.iti__country-list {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e1e5e9 !important;
}

.iti__selected-flag {
  padding: 12px 8px !important;
  border-radius: 8px 0 0 8px !important;
  background-color: #f8f9fa !important;
  border-right: 1px solid #e1e5e9 !important;
}

.iti__selected-flag:hover {
  background-color: #e9ecef !important;
}

.iti__arrow {
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-top: 4px solid #6c757d !important;
}

.iti__country-list .iti__country {
  padding: 8px 12px !important;
  border-bottom: 1px solid #f1f3f4 !important;
}

.iti__country-list .iti__country:hover {
  background-color: #f8f9fa !important;
}

.iti__country-list .iti__country.iti__highlight {
  background-color: #007bff !important;
  color: white !important;
}

/* Phone field container styling */
.woocommerce-billing-fields #billing_phone_field,
.woocommerce-billing-fields #billing-phone_field {
  margin-bottom: 20px !important;
}

.woocommerce-billing-fields #billing_phone_field label,
.woocommerce-billing-fields #billing-phone_field label {
  font-weight: 600 !important;
  color: #343a40 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

/* Responsive design for phone field */
@media (max-width: 768px) {
  #billing-phone, #billing_phone {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 14px 15px !important;
  }

  .iti__selected-flag {
    padding: 14px 8px !important;
  }
}
