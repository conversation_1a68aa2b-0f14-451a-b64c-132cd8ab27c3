var currentCountryCode;
var phoneNumber;
var handingCheckLoyalty = false;
var previousPhoneValue = '';

// Enhanced function to wait for element to be available
function waitForElement(selector, callback, maxAttempts = 50, interval = 100) {
    let attempts = 0;
    const checkElement = () => {
        const element = document.querySelector(selector);
        if (element) {
            callback(element);
        } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(checkElement, interval);
        } else {
            console.log('Enable Plugin: Element not found after maximum attempts:', selector);
        }
    };
    checkElement();
}

// Initialize phone input functionality
function initializePhoneInput(billingPhoneInput) {
    if (!billingPhoneInput || jQuery(billingPhoneInput).data('intl-tel-input-initialized')) {
        return;
    }

    // Mark as initialized to prevent double initialization
    jQuery(billingPhoneInput).data('intl-tel-input-initialized', true);

    phoneNumber = window.intlTelInput(billingPhoneInput, {
        initialCountry: localStorage.getItem('selectedCountry') || "eg",
        geoIpLookup: function(callback) {
            jQuery.get('https://ipapi.co/json/', function(response) {
                var countryCode = (response && response.country_code) ? response.country_code.toLowerCase() : "qa";
                localStorage.setItem('selectedCountry', countryCode);
                callback(countryCode);
            }, "json").fail(function() {
                callback("qa");
            });
        },
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
    });

    // Check if the phone input has a value
    if (billingPhoneInput.value) {
        // Try to guess the country code based on the existing phone number
        var guessedCountry = phoneNumber.getValidationError() === 0 ? phoneNumber.getSelectedCountryData().iso2 : null;
        if (guessedCountry) {
            phoneNumber.setCountry(guessedCountry);
            localStorage.setItem('selectedCountry', guessedCountry);
        }
    }

    currentCountryCode = phoneNumber.s.dialCode;

    billingPhoneInput.addEventListener("countrychange", function() {
        currentCountryCode = phoneNumber.s.dialCode;
        localStorage.setItem('selectedCountry', phoneNumber.getSelectedCountryData().iso2);
        console.log('Enable Plugin - Country changed to:', currentCountryCode, phoneNumber.getSelectedCountryData().iso2);

        // Trigger loyalty check when country changes if there's a phone number
        let currentPhoneValue = jQuery(this).val();
        if (currentPhoneValue && currentPhoneValue !== previousPhoneValue) {
            console.log('Enable Plugin - Triggering loyalty check due to country change');
            handleBillingPhone();
            previousPhoneValue = currentPhoneValue;
        }
    });

    // Clean up phone number format
    jQuery(billingPhoneInput).val(
        jQuery(billingPhoneInput).val().replace('+' + currentCountryCode, '').replace(/\s+/g, '')
    );

    // Store initial value
    previousPhoneValue = jQuery(billingPhoneInput).val();

    // Attach event listeners
    attachPhoneEventListeners(billingPhoneInput);
}

// Attach event listeners to phone input
function attachPhoneEventListeners(billingPhoneInput) {
    console.log('Enable Plugin - Attaching event listeners to phone input');

    // Handle input changes
    jQuery(billingPhoneInput).on('input', function() {
        var currentCountryCodeLocal = currentCountryCode || '1';
        // Remove country code and spaces each time user types or updates
        jQuery(this).val(
            jQuery(this).val().replace('+' + currentCountryCodeLocal, '').replace(/\s+/g, '')
        );
        console.log('Enable Plugin - Phone input changed to:', jQuery(this).val());
    });

    // Handle blur event for loyalty check
    jQuery(billingPhoneInput).on('blur', function() {
        let currentPhoneValue = jQuery(this).val();
        console.log('Enable Plugin - Phone blur event triggered');
        console.log('Enable Plugin - Current phone value:', currentPhoneValue);
        console.log('Enable Plugin - Previous phone value:', previousPhoneValue);
        console.log('Enable Plugin - Loyalty display visible:', jQuery('.enable-loyalty-display').is(':visible'));

        if (currentPhoneValue !== previousPhoneValue || !jQuery('.enable-loyalty-display').is(':visible')) {
            console.log('Enable Plugin - Conditions met, calling handleBillingPhone');
            handleBillingPhone();
            previousPhoneValue = currentPhoneValue;
        } else {
            console.log('Enable Plugin - Conditions not met, skipping loyalty check');
        }
    });

    // Also add change event for additional coverage
    jQuery(billingPhoneInput).on('change', function() {
        let currentPhoneValue = jQuery(this).val();
        console.log('Enable Plugin - Phone change event triggered with value:', currentPhoneValue);

        if (currentPhoneValue !== previousPhoneValue) {
            console.log('Enable Plugin - Phone changed, calling handleBillingPhone');
            handleBillingPhone();
            previousPhoneValue = currentPhoneValue;
        }
    });

    console.log('Enable Plugin - Event listeners attached successfully');
}

// New Function: handleBillingPhone
function handleBillingPhone() {
    // Try both possible IDs for the phone field
    var phone = jQuery('#billing-phone').val() || jQuery('#billing-phone').val();
    console.log('Enable Plugin - handleBillingPhone called with phone:', phone);
    console.log('Enable Plugin - handingCheckLoyalty status:', handingCheckLoyalty);
    console.log('Enable Plugin - currentCountryCode:', currentCountryCode);

    if (phone && !handingCheckLoyalty) {
        handingCheckLoyalty = true;
        console.log('Enable Plugin - Starting loyalty check for phone:', phone);
        checkLoyalty(phone);
    } else {
        if (!phone) {
            console.log('Enable Plugin - No phone number provided');
        }
        if (handingCheckLoyalty) {
            console.log('Enable Plugin - Loyalty check already in progress');
        }
    }
}

// Initialize when DOM is ready and also listen for dynamic content
jQuery(document).ready(function() {
    // Try to initialize immediately with both possible IDs
    waitForElement('#billing-phone', initializePhoneInput);
    waitForElement('#billing-phone', initializePhoneInput);

    // Also try after a short delay for block-based checkout
    setTimeout(function() {
        waitForElement('#billing-phone', initializePhoneInput);
        waitForElement('#billing-phone', initializePhoneInput);
    }, 1000);

    // Listen for WooCommerce checkout updates (for block-based checkout)
    jQuery(document.body).on('updated_checkout', function() {
        setTimeout(function() {
            waitForElement('#billing-phone', initializePhoneInput);
            waitForElement('#billing-phone', initializePhoneInput);
        }, 500);
    });

    // Listen for checkout form updates
    jQuery(document.body).on('checkout_error', function() {
        setTimeout(function() {
            waitForElement('#billing-phone', initializePhoneInput);
            waitForElement('#billing-phone', initializePhoneInput);
        }, 500);
    });
});

    function checkLoyalty(phone) {
        console.log('Enable Plugin - Starting AJAX call to check loyalty');
        console.log('Enable Plugin - AJAX URL:', my_script_params.AJAX_URL);
        console.log('Enable Plugin - Phone:', phone);
        console.log('Enable Plugin - Country Code:', currentCountryCode ?? '+974');
        console.log('Enable Plugin - Nonce:', my_script_params.nonce);

        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'check_loyalty',
                phone: phone,
                countryCode: currentCountryCode ?? '+974',
                nonce: my_script_params.nonce
            },
            beforeSend: function() {
                console.log('Enable Plugin - AJAX request sent');
            },
            success: function(response) {
                handingCheckLoyalty = false;
                console.log('Enable Plugin - AJAX Success - Raw response:', response);
                console.log('Enable Plugin - Response type:', typeof response);

                if (my_script_params.debug) {
                    console.log('Enable Plugin - Loyalty Check Response:', response);
                }

                if (response.success && response.data && response.data.loyaltyTier) {
                    console.log('Enable Plugin - Customer found with loyalty tier:', response.data);
                    const customer = response.data;
                    applyTierDiscount(phone, customer);
                    displayLoyaltyInfo(customer);
                } else {
                    console.log('Enable Plugin - No loyalty tier found or unsuccessful response');
                    console.log('Enable Plugin - Response success:', response.success);
                    console.log('Enable Plugin - Response data:', response.data);

                    // Clear any existing loyalty display
                    jQuery('.enable-loyalty-display').remove();
                    // Trigger checkout update
                    if (jQuery('body').length) {
                        jQuery('body').trigger('update_checkout');
                    }
                    // For classic checkout
                    if (jQuery('button[name="update_cart"]').length) {
                        jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                    }
                    jQuery('.btn-discount').removeClass('checked');
                }
            },
            error: function(xhr, status, error) {
                handingCheckLoyalty = false;
                console.error('Enable Plugin - AJAX Error Details:');
                console.error('  Status:', status);
                console.error('  Error:', error);
                console.error('  Response Text:', xhr.responseText);
                console.error('  Status Code:', xhr.status);

                // Clear any existing loyalty display on error
                jQuery('.enable-loyalty-display').remove();
            },
            complete: function() {
                console.log('Enable Plugin - AJAX request completed');
            }
        });
    }

    function applyTierDiscount(phone, customer) {
        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'apply_tier_discount',
                phone: phone,
                countryCode: currentCountryCode,
                customerTier: customer.loyaltyTier,
                nonce: my_script_params.nonce
            },
            success: function(response) {
                if (jQuery('body').length) {
                    jQuery('body').trigger('update_checkout');
                }
                if (jQuery('button[name="update_cart"]').length) {
                    jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                }
                jQuery('.btn-discount').removeClass('checked');
            },
            error: function(xhr, status, error) {
                console.error('Enable Plugin - Apply Tier Discount Error:', error);
            }
        });
    }

    function applyCoupon(couponId, couponValue, button) {
        let isRemoving = jQuery(button).hasClass('active');

        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: isRemoving ? 'remove_coupon' : 'apply_coupon',
                couponId: couponId,
                couponValue: couponValue,
                nonce: my_script_params.nonce
            },
            success: function(response) {
                if (jQuery('body').length) {
                    jQuery('body').trigger('update_checkout');
                }
                if (jQuery('button[name="update_cart"]').length) {
                    jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                }

                // Remove 'active' class from all coupon buttons
                jQuery('.enable-coupon-btn').removeClass('active');

                if (!isRemoving) {
                    // Add 'active' class to the clicked button
                    jQuery(button).addClass('active');
                }
            },
            error: function(xhr, status, error) {
                console.error('Enable Plugin - Apply Coupon Error:', error);
            }
        });
    }

    function displayLoyaltyInfo(customer) {
        console.log('Enable Plugin - displayLoyaltyInfo called with customer:', customer);

        // Remove existing loyalty display
        jQuery('.enable-loyalty-display').remove();

        let loyaltyHtml = createLoyaltyHtml(customer);
        console.log('Enable Plugin - Created loyalty HTML:', loyaltyHtml);

        // Try multiple insertion points for different checkout types
        let inserted = false;
        let insertionPoint = '';

        // Debug: Log available elements
        console.log('Enable Plugin - Available checkout elements:');
        console.log('  .wp-block-woocommerce-checkout:', jQuery('.wp-block-woocommerce-checkout').length);
        console.log('  .wc-block-checkout__main:', jQuery('.wc-block-checkout__main').length);
        console.log('  form.checkout:', jQuery('form.checkout').length);
        console.log('  form[name="checkout"]:', jQuery('form[name="checkout"]').length);
        console.log('  .woocommerce-checkout:', jQuery('.woocommerce-checkout').length);
        console.log('  .woocommerce:', jQuery('.woocommerce').length);

        // For block-based checkout
        if (jQuery('.wp-block-woocommerce-checkout').length) {
            if (jQuery('.wc-block-checkout__main').length) {
                jQuery('.wc-block-checkout__main').prepend(loyaltyHtml);
                inserted = true;
                insertionPoint = '.wc-block-checkout__main (prepend)';
            } else if (jQuery('.wp-block-woocommerce-checkout').length) {
                jQuery('.wp-block-woocommerce-checkout').prepend(loyaltyHtml);
                inserted = true;
                insertionPoint = '.wp-block-woocommerce-checkout (prepend)';
            }
        }

        // For classic checkout
        if (!inserted && jQuery('form.checkout').length) {
            jQuery('form.checkout').before(loyaltyHtml);
            inserted = true;
            insertionPoint = 'form.checkout (before)';
        }

        // Fallback - insert before any checkout form
        if (!inserted && jQuery('form[name="checkout"]').length) {
            jQuery('form[name="checkout"]').before(loyaltyHtml);
            inserted = true;
            insertionPoint = 'form[name="checkout"] (before)';
        }

        // Final fallback - insert at the beginning of checkout content
        if (!inserted && jQuery('.woocommerce-checkout').length) {
            jQuery('.woocommerce-checkout').prepend(loyaltyHtml);
            inserted = true;
            insertionPoint = '.woocommerce-checkout (prepend)';
        }

        // Last resort - insert in any woocommerce container
        if (!inserted && jQuery('.woocommerce').length) {
            jQuery('.woocommerce').prepend(loyaltyHtml);
            inserted = true;
            insertionPoint = '.woocommerce (prepend)';
        }

        if (inserted) {
            console.log('Enable Plugin - Loyalty info inserted at:', insertionPoint);
            console.log('Enable Plugin - Loyalty display element count:', jQuery('.enable-loyalty-display').length);

            // Add click event to coupon buttons
            jQuery('.enable-coupon-btn').on('click', function(e) {
                e.preventDefault();
                let couponValue = jQuery(this).data('coupon-value');
                let couponId = jQuery(this).data('coupon-id');
                console.log('Enable Plugin - Coupon button clicked:', couponId, couponValue);
                applyCoupon(couponId, couponValue, this);
            });
        } else {
            console.error('Enable Plugin: Could not find suitable location to display loyalty info');
            console.log('Enable Plugin: Available elements on page:', jQuery('*').length);
        }
    }

    function createLoyaltyHtml(customer) {
        let loyaltyHtml = '<div class="enable-loyalty-display" style="background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">' +
            '<h3 style="color: #343a40; font-size: 1.2em; margin-bottom: 10px; border-bottom: 2px solid #007bff; padding-bottom: 5px;">Your Loyalty Benefits</h3>' +
            '<div class="loyalty-info" style="display: flex; flex-direction: column; margin-bottom: 10px;">' +
            '<p class="tier-name" style="background-color: #e9ecef; padding: 8px; border-radius: 5px; margin-bottom: 5px;">Tier: ' + customer.loyaltyTier.name + '</p>' +
            '<p class="tier-discount" style="background-color: #e9ecef; padding: 8px; border-radius: 5px; margin-bottom: 5px;">Discount: ' + (customer.loyaltyTier.tierDiscount * 100).toFixed(2) + '%</p>' +
            '<p class="loyalty-points" style="background-color: #e9ecef; padding: 8px; border-radius: 5px; margin-bottom: 5px;">Loyalty Points: ' + customer.loyaltyPoints + '</p>' +
            '</div>';

        if (customer.coupons && customer.coupons.length > 0) {
            loyaltyHtml += createCouponHtml(customer.coupons, customer.loyaltyPoints);
        }

        loyaltyHtml += '</div>';
        console.log('Enable Plugin - Created loyalty HTML with inline styles');
        return loyaltyHtml;
    }

    function createCouponHtml(coupons, customerLoyaltyPoints) {
        let couponHtml = '<h4>Your Available Coupons</h4><div class="coupon-list">';

        coupons.forEach(function(coupon) {
            if ((!coupon.flatDiscountAmount && !coupon.percentageAmount )|| coupon.loyaltyPointCost > customerLoyaltyPoints) return;

            let couponValue = coupon.flatDiscountAmount
                ? coupon.flatDiscountAmount.toFixed(2) + ' QAR'
                : (coupon.percentageAmount * 100).toFixed(2) + '%';

            couponHtml += '<button class="enable-coupon-btn" data-coupon-id="' + coupon._id + '" data-coupon-value="' + coupon.flatDiscountAmount + '"> ' +
                '<span class="coupon-value">' + couponValue + '</span>' +
                (coupon.loyaltyPointCost ? '<span class="coupon-cost">Cost: ' + coupon.loyaltyPointCost + ' points</span>' : '') +
                '</button>';
        });

        couponHtml += '</div>';
        return couponHtml;
    }
