var currentCountryCode;


jQuery(document).on('input', '#billing_phone', function() {
    var currentCountryCode = '1';  // Example country code (US)

    // Remove country code and spaces each time user types or updates
    jQuery('#billing_phone').val(
        jQuery('#billing_phone').val().replace('+' + currentCountryCode, '').replaceAll(/\s+/g, '')
    );
});
jQuery(document).ready(function() {
  let handingCheckLoyalty = false;
  let previousPhoneValue = jQuery('#billing_phone').val();
  var billingPhoneInput = document.querySelector("#billing_phone");

  if (jQuery(billingPhoneInput).length) {
        let phoneNumber = window.intlTelInput(billingPhoneInput, {
            initialCountry: localStorage.getItem('selectedCountry') || "eg",
            geoIpLookup: function(callback) {
                jQuery.get('https://ipapi.co/json/', function(response) {
                    var countryCode = (response && response.country_code) ? response.country_code.toLowerCase() : "qa";
                    localStorage.setItem('selectedCountry', countryCode);
                    callback(countryCode);
                }, "json").fail(function() {
                    callback("qa");
                });
            },
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
        });

        // Check if the phone input has a value
        if (billingPhoneInput.value) {
            // Try to guess the country code based on the existing phone number
            var guessedCountry = phoneNumber.getValidationError() === 0 ? phoneNumber.getSelectedCountryData().iso2 : null;
            if (guessedCountry) {
                phoneNumber.setCountry(guessedCountry);
                localStorage.setItem('selectedCountry', guessedCountry);
            }
        }

        currentCountryCode = phoneNumber.s.dialCode;

        billingPhoneInput.addEventListener("countrychange", function() {
            currentCountryCode = phoneNumber.s.dialCode;
            localStorage.setItem('selectedCountry', phoneNumber.getSelectedCountryData().iso2);
        });

        jQuery('#billing_phone').val(
            jQuery('#billing_phone').val().replace('+' + currentCountryCode, '').replaceAll(/\s+/g, '')
        );
    }

    // New Function: handleBillingPhone
    function handleBillingPhone() {
        var phone = jQuery('#billing_phone').val();
        if (phone && !handingCheckLoyalty) {
          handingCheckLoyalty = true;
            checkLoyalty(phone);
            // Add any additional logic you want to execute
            console.log('handleBillingPhone called with phone:', phone);
        }
    }

    // Call handleBillingPhone on page load if billing_phone has a value
    handleBillingPhone();

    // Attach event listener to billing_phone for changes
    jQuery('#billing_phone').on('blur', function() {
        let currentPhoneValue = jQuery(this).val();
        if (currentPhoneValue !== previousPhoneValue || !jQuery('.enable-loyalty-display').is(':visible')) {
            handleBillingPhone();
            previousPhoneValue = currentPhoneValue;
        }
    });

    function checkLoyalty(phone) {
        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'check_loyalty',
                phone: phone,
                countryCode: currentCountryCode ?? '+974'
            },
            success: function(response) {
                handingCheckLoyalty  = false;
                console.log(response);
                const customer = response.data;
                if (customer && customer.loyaltyTier) {
                    applyTierDiscount(phone, customer);
                    displayLoyaltyInfo(customer);
                } else {
                    jQuery('body').trigger('update_checkout');
                    jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                    jQuery('.btn-discount').removeClass('checked');
                }
            }
        });
    }

    function applyTierDiscount(phone, customer) {
        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'apply_tier_discount',
                phone: phone,
                countryCode: currentCountryCode,
                customerTier: customer.loyaltyTier
            },
            success: function(response) {
                jQuery('body').trigger('update_checkout');
                jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                jQuery('.btn-discount').removeClass('checked');
            }
        });
    }

    function applyCoupon(couponId, couponValue, button) {
        let isRemoving = jQuery(button).hasClass('active');

        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: isRemoving ? 'remove_coupon' : 'apply_coupon',
                couponId: couponId,
                couponValue: couponValue
            },
            success: function(response) {
                jQuery('body').trigger('update_checkout');
                jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');

                // Remove 'active' class from all coupon buttons
                jQuery('.enable-coupon-btn').removeClass('active');

                if (!isRemoving) {
                    // Add 'active' class to the clicked button
                    jQuery(button).addClass('active');
                }
            }
        });
    }

    function displayLoyaltyInfo(customer) {
        // Remove existing loyalty display
        jQuery('.enable-loyalty-display').remove();

        let loyaltyHtml = createLoyaltyHtml(customer);

        // Insert before the checkout form
        jQuery('form.checkout').before(loyaltyHtml);

        // Add click event to coupon buttons
        jQuery('.enable-coupon-btn').on('click', function(e) {
            e.preventDefault();
            let couponValue = jQuery(this).data('coupon-value');
            let couponId = jQuery(this).data('coupon-id');
            applyCoupon(couponId, couponValue, this);
        });
    }

    function createLoyaltyHtml(customer) {
        let loyaltyHtml = '<div class="enable-loyalty-display">' +
            '<h3>Your Loyalty Benefits</h3>' +
            '<div class="loyalty-info">' +
            '<p class="tier-name">Tier: ' + customer.loyaltyTier.name + '</p>' +
            '<p class="tier-discount">Discount: ' + (customer.loyaltyTier.tierDiscount * 100).toFixed(2) + '%</p>' +
            '<p class="loyalty-points">Loyalty Points: ' + customer.loyaltyPoints + '</p>' +
            '</div>';

        if (customer.coupons && customer.coupons.length > 0) {
            loyaltyHtml += createCouponHtml(customer.coupons, customer.loyaltyPoints);
        }

        loyaltyHtml += '</div>';
        return loyaltyHtml;
    }

    function createCouponHtml(coupons, customerLoyaltyPoints) {
        let couponHtml = '<h4>Your Available Coupons</h4><div class="coupon-list">';

        coupons.forEach(function(coupon) {
            if ((!coupon.flatDiscountAmount && !coupon.percentageAmount )|| coupon.loyaltyPointCost > customerLoyaltyPoints) return;

            let couponValue = coupon.flatDiscountAmount
                ? coupon.flatDiscountAmount.toFixed(2) + ' QAR'
                : (coupon.percentageAmount * 100).toFixed(2) + '%';

            couponHtml += '<button class="enable-coupon-btn" data-coupon-id="' + coupon._id + '" data-coupon-value="' + coupon.flatDiscountAmount + '"> ' +
                '<span class="coupon-value">' + couponValue + '</span>' +
                (coupon.loyaltyPointCost ? '<span class="coupon-cost">Cost: ' + coupon.loyaltyPointCost + ' points</span>' : '') +
                '</button>';
        });

        couponHtml += '</div>';
        return couponHtml;
    }
});
