var currentCountryCode;
var phoneNumber;
var handingCheckLoyalty = false;
var previousPhoneValue = '';

// Enhanced function to wait for element to be available
function waitForElement(selector, callback, maxAttempts = 50, interval = 100) {
    let attempts = 0;
    const checkElement = () => {
        const element = document.querySelector(selector);
        if (element) {
            callback(element);
        } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(checkElement, interval);
        } else {
            console.log('Enable Plugin: Element not found after maximum attempts:', selector);
        }
    };
    checkElement();
}

// Initialize phone input functionality
function initializePhoneInput(billingPhoneInput) {
    if (!billingPhoneInput || jQuery(billingPhoneInput).data('intl-tel-input-initialized')) {
        return;
    }

    // Mark as initialized to prevent double initialization
    jQuery(billingPhoneInput).data('intl-tel-input-initialized', true);

    phoneNumber = window.intlTelInput(billingPhoneInput, {
        initialCountry: localStorage.getItem('selectedCountry') || "eg",
        geoIpLookup: function(callback) {
            jQuery.get('https://ipapi.co/json/', function(response) {
                var countryCode = (response && response.country_code) ? response.country_code.toLowerCase() : "qa";
                localStorage.setItem('selectedCountry', countryCode);
                callback(countryCode);
            }, "json").fail(function() {
                callback("qa");
            });
        },
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
    });

    // Check if the phone input has a value
    if (billingPhoneInput.value) {
        // Try to guess the country code based on the existing phone number
        var guessedCountry = phoneNumber.getValidationError() === 0 ? phoneNumber.getSelectedCountryData().iso2 : null;
        if (guessedCountry) {
            phoneNumber.setCountry(guessedCountry);
            localStorage.setItem('selectedCountry', guessedCountry);
        }
    }

    currentCountryCode = phoneNumber.s.dialCode;

    billingPhoneInput.addEventListener("countrychange", function() {
        currentCountryCode = phoneNumber.s.dialCode;
        localStorage.setItem('selectedCountry', phoneNumber.getSelectedCountryData().iso2);
    });

    // Clean up phone number format
    jQuery(billingPhoneInput).val(
        jQuery(billingPhoneInput).val().replace('+' + currentCountryCode, '').replace(/\s+/g, '')
    );

    // Store initial value
    previousPhoneValue = jQuery(billingPhoneInput).val();

    // Attach event listeners
    attachPhoneEventListeners(billingPhoneInput);
}

// Attach event listeners to phone input
function attachPhoneEventListeners(billingPhoneInput) {
    // Handle input changes
    jQuery(billingPhoneInput).on('input', function() {
        var currentCountryCodeLocal = currentCountryCode || '1';
        // Remove country code and spaces each time user types or updates
        jQuery(this).val(
            jQuery(this).val().replace('+' + currentCountryCodeLocal, '').replace(/\s+/g, '')
        );
    });

    // Handle blur event for loyalty check
    jQuery(billingPhoneInput).on('blur', function() {
        let currentPhoneValue = jQuery(this).val();
        if (currentPhoneValue !== previousPhoneValue || !jQuery('.enable-loyalty-display').is(':visible')) {
            handleBillingPhone();
            previousPhoneValue = currentPhoneValue;
        }
    });
}

// New Function: handleBillingPhone
function handleBillingPhone() {
    var phone = jQuery('#billing_phone').val();
    if (phone && !handingCheckLoyalty) {
        handingCheckLoyalty = true;
        checkLoyalty(phone);
        console.log('handleBillingPhone called with phone:', phone);
    }
}

// Initialize when DOM is ready and also listen for dynamic content
jQuery(document).ready(function() {
    // Try to initialize immediately
    waitForElement('#billing_phone', initializePhoneInput);

    // Also try after a short delay for block-based checkout
    setTimeout(function() {
        waitForElement('#billing_phone', initializePhoneInput);
    }, 1000);

    // Listen for WooCommerce checkout updates (for block-based checkout)
    jQuery(document.body).on('updated_checkout', function() {
        setTimeout(function() {
            waitForElement('#billing_phone', initializePhoneInput);
        }, 500);
    });

    // Listen for checkout form updates
    jQuery(document.body).on('checkout_error', function() {
        setTimeout(function() {
            waitForElement('#billing_phone', initializePhoneInput);
        }, 500);
    });
});

    function checkLoyalty(phone) {
        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'check_loyalty',
                phone: phone,
                countryCode: currentCountryCode ?? '+974',
                nonce: my_script_params.nonce
            },
            success: function(response) {
                handingCheckLoyalty = false;
                if (my_script_params.debug) {
                    console.log('Enable Plugin - Loyalty Check Response:', response);
                }

                if (response.success && response.data && response.data.loyaltyTier) {
                    const customer = response.data;
                    applyTierDiscount(phone, customer);
                    displayLoyaltyInfo(customer);
                } else {
                    // Clear any existing loyalty display
                    jQuery('.enable-loyalty-display').remove();
                    // Trigger checkout update
                    if (jQuery('body').length) {
                        jQuery('body').trigger('update_checkout');
                    }
                    // For classic checkout
                    if (jQuery('button[name="update_cart"]').length) {
                        jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                    }
                    jQuery('.btn-discount').removeClass('checked');
                }
            },
            error: function(xhr, status, error) {
                handingCheckLoyalty = false;
                console.error('Enable Plugin - Loyalty Check Error:', error);
                // Clear any existing loyalty display on error
                jQuery('.enable-loyalty-display').remove();
            }
        });
    }

    function applyTierDiscount(phone, customer) {
        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: 'apply_tier_discount',
                phone: phone,
                countryCode: currentCountryCode,
                customerTier: customer.loyaltyTier,
                nonce: my_script_params.nonce
            },
            success: function(response) {
                if (jQuery('body').length) {
                    jQuery('body').trigger('update_checkout');
                }
                if (jQuery('button[name="update_cart"]').length) {
                    jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                }
                jQuery('.btn-discount').removeClass('checked');
            },
            error: function(xhr, status, error) {
                console.error('Enable Plugin - Apply Tier Discount Error:', error);
            }
        });
    }

    function applyCoupon(couponId, couponValue, button) {
        let isRemoving = jQuery(button).hasClass('active');

        jQuery.ajax({
            url: my_script_params.AJAX_URL,
            type: 'POST',
            data: {
                action: isRemoving ? 'remove_coupon' : 'apply_coupon',
                couponId: couponId,
                couponValue: couponValue,
                nonce: my_script_params.nonce
            },
            success: function(response) {
                if (jQuery('body').length) {
                    jQuery('body').trigger('update_checkout');
                }
                if (jQuery('button[name="update_cart"]').length) {
                    jQuery('button[name="update_cart"]').removeAttr('disabled').trigger('click');
                }

                // Remove 'active' class from all coupon buttons
                jQuery('.enable-coupon-btn').removeClass('active');

                if (!isRemoving) {
                    // Add 'active' class to the clicked button
                    jQuery(button).addClass('active');
                }
            },
            error: function(xhr, status, error) {
                console.error('Enable Plugin - Apply Coupon Error:', error);
            }
        });
    }

    function displayLoyaltyInfo(customer) {
        // Remove existing loyalty display
        jQuery('.enable-loyalty-display').remove();

        let loyaltyHtml = createLoyaltyHtml(customer);

        // Try multiple insertion points for different checkout types
        let inserted = false;

        // For block-based checkout
        if (jQuery('.wp-block-woocommerce-checkout').length) {
            if (jQuery('.wc-block-checkout__main').length) {
                jQuery('.wc-block-checkout__main').prepend(loyaltyHtml);
                inserted = true;
            } else if (jQuery('.wp-block-woocommerce-checkout').length) {
                jQuery('.wp-block-woocommerce-checkout').prepend(loyaltyHtml);
                inserted = true;
            }
        }

        // For classic checkout
        if (!inserted && jQuery('form.checkout').length) {
            jQuery('form.checkout').before(loyaltyHtml);
            inserted = true;
        }

        // Fallback - insert before any checkout form
        if (!inserted && jQuery('form[name="checkout"]').length) {
            jQuery('form[name="checkout"]').before(loyaltyHtml);
            inserted = true;
        }

        // Final fallback - insert at the beginning of checkout content
        if (!inserted && jQuery('.woocommerce-checkout').length) {
            jQuery('.woocommerce-checkout').prepend(loyaltyHtml);
            inserted = true;
        }

        if (inserted) {
            // Add click event to coupon buttons
            jQuery('.enable-coupon-btn').on('click', function(e) {
                e.preventDefault();
                let couponValue = jQuery(this).data('coupon-value');
                let couponId = jQuery(this).data('coupon-id');
                applyCoupon(couponId, couponValue, this);
            });
        } else {
            console.log('Enable Plugin: Could not find suitable location to display loyalty info');
        }
    }

    function createLoyaltyHtml(customer) {
        let loyaltyHtml = '<div class="enable-loyalty-display">' +
            '<h3>Your Loyalty Benefits</h3>' +
            '<div class="loyalty-info">' +
            '<p class="tier-name">Tier: ' + customer.loyaltyTier.name + '</p>' +
            '<p class="tier-discount">Discount: ' + (customer.loyaltyTier.tierDiscount * 100).toFixed(2) + '%</p>' +
            '<p class="loyalty-points">Loyalty Points: ' + customer.loyaltyPoints + '</p>' +
            '</div>';

        if (customer.coupons && customer.coupons.length > 0) {
            loyaltyHtml += createCouponHtml(customer.coupons, customer.loyaltyPoints);
        }

        loyaltyHtml += '</div>';
        return loyaltyHtml;
    }

    function createCouponHtml(coupons, customerLoyaltyPoints) {
        let couponHtml = '<h4>Your Available Coupons</h4><div class="coupon-list">';

        coupons.forEach(function(coupon) {
            if ((!coupon.flatDiscountAmount && !coupon.percentageAmount )|| coupon.loyaltyPointCost > customerLoyaltyPoints) return;

            let couponValue = coupon.flatDiscountAmount
                ? coupon.flatDiscountAmount.toFixed(2) + ' QAR'
                : (coupon.percentageAmount * 100).toFixed(2) + '%';

            couponHtml += '<button class="enable-coupon-btn" data-coupon-id="' + coupon._id + '" data-coupon-value="' + coupon.flatDiscountAmount + '"> ' +
                '<span class="coupon-value">' + couponValue + '</span>' +
                (coupon.loyaltyPointCost ? '<span class="coupon-cost">Cost: ' + coupon.loyaltyPointCost + ' points</span>' : '') +
                '</button>';
        });

        couponHtml += '</div>';
        return couponHtml;
    }
});
