<?php
/*
Plugin Name: Enable Woocommerce Integration
Description: WooCommerce plugin to integrate with Enable (Payment Gateway Integration, Order Creation, Order Status Update, Loyalty Program Integration)
Version: 1.14
Author: <PERSON><PERSON>, <PERSON> ,Asmar

*/


if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Include the main EnableOrder class
require_once plugin_dir_path(__FILE__) . 'includes/class-enable-order.php';

// Include the EnablePayment class
require_once plugin_dir_path(__FILE__) . 'includes/class-enable-payment.php';

// Include the new class
require_once plugin_dir_path(__FILE__) . 'includes/class-enable-helper.php';

// Include the new class
require_once plugin_dir_path(__FILE__) . 'includes/class-enable-loyalty.php';

// Include the new class
new EnableOrder();
new EnablePayment();
new EnableLoyalty();
