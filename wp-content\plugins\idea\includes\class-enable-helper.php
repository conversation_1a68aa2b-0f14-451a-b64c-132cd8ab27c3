<?php

class EnableHelper
{
    private const BACKEND_URL = 'https://backend.enable.tech';
    private const BACKEND_STAGING_URL = 'https://backend.staging.enable.tech';

    public function __construct() {}

    public function extractPaymentData(WC_Order $order, bool $testMode): array
    {
        $totals = $this->getOrderTotalAmount($order);
        $customerName = $this->getCustomerName($order);
        return [
            'customer_name' => $customerName['first_name'] . ' ' . $customerName['last_name'],
            'customer_phone' => $this->getCustomerPhone($order),
            'country_code' => '',
            'amount' => $totals['totalAmountAfterDiscount'],
            'language' => get_locale() == 'en_US' ? 'english' : 'arabic',
            'payment_method' => 'credit_cards',
            'is_test' => $testMode ? 1 : '',
            'callback_url' => site_url('/') . 'wc-api/enable_gateway_response',
            'source' => 'webstore',
        ];
    }

    public function extractOrderData(WC_Order $order, bool $testMode, string $deliveryType): array
    {
        $slotDetails = $this->getSlotDetails($order);
        $discountsAndCoupon = $this->getDiscounts($order);
        $totals = $this->getOrderTotalAmount($order);
        $fullName = $this->getCustomerName($order);
        $orderData = [
            'first_name' => $fullName['first_name'],
            'last_name' => $fullName['last_name'],
            'phone' => $this->getCustomerPhone($order),
            'country_code' => '',
            'email' => $this->getCustomerEmail($order),
            'pickup_date' => '',
            'pickup_time' => '',
            'delivery_date' => $this->getDeliveryDate($order),
            'delivery_time' => $slotDetails['to'],
            'invoice_number' => $order->get_id(),
            'invoiced_amount' => $totals['totalAmount'],
            'delivery_amount' => $this->getDeliveryFee($order),
            'discount' => $order->get_discount_total(),
            'total_amount' => $totals['totalAmount'],
            'total_amount_after_discount' => $totals['totalAmountAfterDiscount'],
            'order_remarks' => $order->get_customer_note(),
            'payment_method' => $this->getOrderPaymentMethod($order),
            'source' => 'webstore',
            'creationSource' => 'woocommerce',
            'delivery_action' => 'delivery_location',
            'delivery_address' => $this->getDeliveryAddress($order, $fullName['first_name']),
            'delivery_type' => $deliveryType,
            'orderType' => 'restaurant',
            'discounts' => $discountsAndCoupon['discounts'],
            'couponId' => $discountsAndCoupon['couponId'],
            'delivery_slot_from' => $slotDetails['from'],
            'delivery_slot_to' => $slotDetails['to'],
            'is_test' => $testMode ? 1 : '',
            'items' => $this->getOrderItems($order),
            'paymentCode' => $order->get_meta('EBL_paymentCode'),
            'cardMessage' => $order->get_meta('order_message_on_card_box')
        ];

        $this->handleGiftOrder($order, $orderData);

        return $orderData;
    }

    public function performIntegrationRequest(array $requestDetails, WC_Order $order, string $apiKey, bool $includeOrderId, bool $debugMode, bool $testingMode): WP_Error|array|null
    {
        try {
            $requestURL = $testingMode ? self::BACKEND_STAGING_URL : self::BACKEND_URL;

            if ($debugMode) {
                $this->debugOutput($requestURL, $requestDetails, $order);
                return null;
            }

            $headers = ['saasapikey' => $apiKey];
            if ($includeOrderId) {
                $headers['X-EXTERNAL-ORDER-ID'] = $order->get_id();
            }

            $response = wp_remote_post($requestURL . '/integration/order_payment', [
                'method' => 'POST',
                'timeout' => 45,
                'headers' => $headers,
                'sslverify' => false,
                'body' => $requestDetails,
            ]);

            if (is_wp_error($response)) {
                error_log('Enable: Integration Request Error: ' . $response->get_error_message());
                $order->update_meta_data('EBL_enableOrderCode', 'Error');
                return $response;
            }

            return $response;
        } catch (Exception $e) {
            error_log('Enable: Unexpected Integration Request Error: ' . $e->getMessage());
            return new WP_Error('Enable: integration_request_error', 'An unexpected error occurred during the integration request.');
        }
    }

    private function getCustomerEmail(WC_Order $order): string
    {
        // Check for email in billing details
        $email = $order->get_billing_email();

        // If not found in billing, check meta data
        if (empty($email)) {
            $email = $order->get_meta('_billing_email');
        }

        // As a last resort, check user's email if the order is associated with a user
        if (empty($email)) {
            $user_id = $order->get_user_id();
            if ($user_id) {
                $user = get_user_by('id', $user_id);
                if ($user) {
                    $email = $user->user_email;
                }
            }
        }

        return trim($email);
    }

    private function getCustomerName(WC_Order $order): array
    {
        // Check for first name
        $first_name = $order->get_meta('_billing_first_name');

        if (empty($first_name)) {
            $first_name = $order->get_billing_first_name();
        }

        if (empty($first_name)) {
            $first_name = $order->get_shipping_first_name();
        }

        // Check for last name
        $last_name = $order->get_meta('_billing_last_name');

        if (empty($last_name)) {
            $last_name = $order->get_billing_last_name();
        }

        if (empty($last_name)) {
            $last_name = $order->get_shipping_last_name();
        }

        // If either first name or last name is still empty, check meta data
        if (empty($first_name) || empty($last_name)) {
            $full_name = $order->get_meta('_billing_full_name');
            if (!empty($full_name)) {
                // If meta data exists, split it into first and last name
                $name_parts = explode(' ', trim($full_name), 2);
                $first_name = $name_parts[0];
                $last_name = $name_parts[1] ?? '';
            }
        }

        return [
            'first_name' => trim($first_name),
            'last_name' => trim($last_name)
        ];
    }

    private function getCustomerPhone(WC_Order $order): string
    {

        $phone = $order->get_meta('_billing_phone');

        if (empty($phone)) {
            // Check for phone in shipping details
            $phone = $order->get_billing_phone();
        }

        // If not found in shipping, check billing details
        if (empty($phone)) {
            $phone = $order->get_shipping_phone();
        }

        // If still not found, check meta data
        if (empty($phone)) {
            $phone = $order->get_meta('_shipping_Mobile_number');
        }

        return trim($phone);
    }

    private function getOrderPaymentMethod(WC_Order $order): string
    {
        $paymentMethod = $order->get_payment_method();
        if ($paymentMethod == 'cod') {
            return 'cash';
        }
        return $order->get_meta('EBL_paymentCode') ? 'online' : 'prepaid';
    }

    private function getOrderTotalAmount(WC_Order $order): array
    {
        $subtotal = $order->get_subtotal();
        $total = $order->get_total();
        $discountAmount = $subtotal - $total;

        return [
            'totalAmount' => $this->formatAmount($subtotal),
            'totalAmountAfterDiscount' => $this->formatAmount($total),
            'discountAmount' => $this->formatAmount($discountAmount)
        ];
    }

    private function getDiscounts(WC_Order $order): array
    {
        $discounts = $order->get_meta('eblDiscounts') ?: [];
        if (is_string($discounts)) {
            $discounts = json_decode($discounts, true);
        }

        $orderFees = $order->get_fees();
        foreach ($orderFees as $fee) {
            if ($fee->get_name() === __('Tier Discount', 'enable-loyalty')) {
                $tierDiscount = abs($fee->get_total());
                $discounts[] = [
                    "type" => "percent",
                    "source" => "Loyalty Tier Discount",
                    "applyTo" => "cart",
                    "amount" => ($tierDiscount / $order->get_subtotal()) * 100
                ];
                break;
            }
            $enableLoyaltyDiscount = WC()->session->get('enable_loyalty_discount');
            $couponId = $enableLoyaltyDiscount ? $enableLoyaltyDiscount['couponId'] : '';

            if ($couponId) {
                $discounts[] = [
                    "type" => "flat",
                    "source" => "Coupon Discount",
                    "applyTo" => "cart",
                    "amount" => $enableLoyaltyDiscount['coupon_discount'],
                    "couponId" => $couponId
                ];
            }
        }
        return [
            'discounts' => $discounts ?: [],
            'couponId' => $order->get_meta('eblCouponId') ?: $couponId,
        ];
    }

    private function getDeliveryFee(WC_Order $order): float
    {
        $total_fee = 0;
        foreach ($order->get_items('fee') as $item_fee) {
            if (is_a($item_fee, 'WC_Order_Item_Fee') && strpos($item_fee->get_name(), 'Discount') === false) {
                $total_fee += $item_fee->get_total();
            }
        }
        return $total_fee ?: $order->get_shipping_total();
    }

    private function getOrderItems(WC_Order $order): array
    {
        return array_reduce($order->get_items(), function ($items, $item) {
            if ($item instanceof WC_Order_Item_Product) {
                $product = $item->get_product();
                $price = $product->get_price();
                $quantity = $item->get_quantity();
                $totalAmount = $price * $quantity;

                $items[] = [
                    'special_instructions' => $this->getItemSpecialInstructions($item),
                    'index' => count($items) + 1,
                    'name' => $item->get_name(),
                    'quantity' => $quantity,
                    'price' => $price,
                    'code' => $product->get_sku(),
                    'plu' => $product->get_sku(),
                    'totalAmount' => $totalAmount,
                    'totalAmountAfterDiscount' => $totalAmount,
                    'itemReference' => $product->get_sku(),
                    'externalImage' => $this->getExternalImage($product)
                ];
            }
            return $items;
        }, []);
    }

    private function getExternalImage(WC_Product $product): string
    {
        $image_id = $product->get_image_id();
        if ($image_id) {
            $image_url = wp_get_attachment_url($image_id);
            return $image_url ?: '';
        }
    }

    private function getItemSpecialInstructions($item)
    {
        try {
            $meta = $item->get_meta('_alg_wc_pif_global');
            return is_array($meta) && isset($meta[0]['_value']) ? $meta[0]['_value'] : '';
        } catch (Exception $e) {
            return '';
        }
    }

    private function getDeliveryDate(WC_Order $order): string
    {
        $dateFields = ['wdc_delivery_date', 'delivery_date', 'ywcdd_order_shipping_date', 'order_delivering_date'];
        foreach ($dateFields as $field) {
            $date = $order->get_meta($field);
            if ($date) {
                return $this->formatDate($date, $field === 'wdc_delivery_date' || $field === 'order_delivering_date');
            }
        }
        return date('Y-m-d');
    }

    private function formatDate(string $date, bool $needsReformat): string
    {
        if ($needsReformat) {
            $dateArray = explode("-", $date);
            return date('Y-m-d', strtotime($dateArray[2] . '-' . $dateArray[1] . '-' . $dateArray[0]));
        }
        return $date;
    }

    private function getDeliveryAddress(WC_Order $order, string $first_name): array
    {
        $addressFields = [
            'zone_number' => ['wdc_national_zone', 'shipping_zone', '_shipping_zone', '_shipping_zone_number', '_billing_zone_number', '_billing_wooccm11', '_billing_Zone', '_billing_zone'],
            'building_number' => ['wdc_national_building', '_shipping_building_number', '_shipping_Building_number', 'shipping_build', '_billing_building_number', '_billing_wooccm12', '_billing_Building_number', '_billing_build'],
            'street_number' => ['wdc_national_street', '_shipping_street', 'shipping_street', '_shipping_address_1', '_billing_street_number', '_shipping_street_number', '_billing_wooccm13', '_billing_Street', '_billing_Street', '_billing_street'],
            'area' => ['_shipping_area', '_shipping_region_state', '_billing_area', 'wdc_area', 'shipping_region_state', '_shipping_region_state', 'billing_state', '_billing_state'],
            'city' => ['_shipping_city', '_shipping_address_1', '_shipping_region_select', 'wdc_area', '_billing_city', '_billing_wooccm14', '_billing_City'],
        ];

        $address = [
            'nickname' => 'LOC-' . $first_name,
            'building_name' => $order->get_meta('wdc_compound'),
            'building_house_number' => $order->get_meta('wdc_house'),
            'office_number' => $order->get_meta('wdc_apartment'),
            'locationType' => 'nationalAddress',
            'pin_lat' => $order->get_meta('wdc_lat') ?: '25.31781806',
            'pin_lng' => $order->get_meta('wdc_lon') ?: '51.52669483',
        ];

        foreach ($addressFields as $field => $metaKeys) {
            $address[$field] = $this->getFirstNonEmptyMeta($order, $metaKeys);
        }

        $address['pin_link'] = "https://maps.google.com/?q={$address['pin_lat']},{$address['pin_lng']}";

        return $address;
    }

    private function getFirstNonEmptyMeta(WC_Order $order, array $metaKeys): string
    {
        foreach ($metaKeys as $key) {
            $value = $order->get_meta($key);
            if ($value !== '') {
                return $value;
            }
        }
        return '';
    }

    private function getSlotDetails(WC_Order $order): array
    {
        $defaultSlot = ['from' => '19:00', 'to' => '23:30'];

        try {
            $slotMeta = $order->get_meta('wdc_delivery_slot') ?: $order->get_meta('delivery_time');
            if ($slotMeta) {
                list($from, $to) = explode(' - ', $slotMeta);
                return ['from' => $from, 'to' => $to];
            }

            $orderDeliveringTime = $order->get_meta('order_delivering_time');
            if ($orderDeliveringTime) {
                // value like "600PMto900PM"
                list($from, $to) = explode('to', $orderDeliveringTime);
                return [
                    'from' => $this->formatTime($from),
                    'to' => $this->formatTime($to)
                ];
            }
        } catch (Exception $e) {
            // Log the exception if needed
        }

        return $defaultSlot;
    }

    /**
     * @param string $time Expected format like "600PM" or "1130AM"
     * @return string Output like "18:00" or "11:30"
     */
    private function formatTime(string $time): string
    {
        $is_two_digit_hour = strlen($time) === 6;
        $hour_digits = $is_two_digit_hour ? 2 : 1;
        $hour = intval(substr($time, 0, $hour_digits));
        $minute = intval(substr($time, $hour_digits, 2));

        $is_pm = substr($time, -2) === 'PM';

        if ($is_pm && $hour !== 12) {
            $hour += 12;
        } else if (!$is_pm && $hour === 12) {
            $hour = 0;
        }

        return sprintf('%02d:%02d', $hour, $minute);
    }

    private function handleGiftOrder(WC_Order $order, array &$orderData): void
    {
        $isGift = $this->isGiftOrder($order);

        if ($isGift) {
            $this->setGiftOrderData($order, $orderData);
        }
    }

    private function isGiftOrder(WC_Order $order): bool
    {
        $billingName = $this->getFullName($order->get_billing_first_name(), $order->get_billing_last_name());
        $shippingName = $this->getFullName($order->get_shipping_first_name(), $order->get_shipping_last_name());

        $isNameDifferent = !empty($billingName) && !empty($shippingName) && $billingName !== $shippingName;

        return $order->get_meta('wdc_is_gift') == 1
            || ($order->get_shipping_first_name() && $this->hasRecipientContact($order))
            || $isNameDifferent;
    }

    private function getFullName(string $firstName, string $lastName): string
    {
        return trim($firstName . ' ' . $lastName);
    }

    private function hasRecipientContact(WC_Order $order): bool
    {
        return $order->get_meta('_shipping_mobile')
            || $order->get_meta('_shipping_Mobile_number')
            || $order->get_meta('wdc_recipient_no');
    }

    private function setGiftOrderData(WC_Order $order, array &$orderData): void
    {
        $orderData['is_gift'] = 1;
        $orderData['recipient_name'] = $this->getRecipientName($order);
        $orderData['recipient_phone'] = $this->getRecipientPhone($order);
        $orderData['recipient_country_code'] = '';
    }

    private function getRecipientName(WC_Order $order): string
    {
        $shippingName = $this->getFullName($order->get_shipping_first_name(), $order->get_shipping_last_name());
        return $shippingName ?: $order->get_meta('wdc_recipient_name');
    }

    private function getRecipientPhone(WC_Order $order): string
    {
        return $order->get_meta('_shipping_Mobile_number')
            ?: $order->get_meta('_shipping_mobile')
            ?: $order->get_meta('wdc_recipient_no');
    }

    private function formatAmount(float $amount): string
    {
        return number_format($amount, wc_get_price_decimals(), '.', '');
    }

    private function debugOutput(string $requestURL, array $requestDetails, WC_Order $order): void
    {
        echo "Request URL: $requestURL<br>";
        print_r($requestDetails);
        print_r($order);
    }

    public function lookupCustomerDetails($phone, $countryCode, $apiKey, $testMode){
      try{
        $requestURL = $testMode ? self::BACKEND_STAGING_URL : self::BACKEND_URL;
        $response = wp_remote_get($requestURL . '/integration/customer/details/'.$countryCode.'/'.$phone, [
            'method' => 'GET',
            'timeout' => 45,
            'headers' => ['saasapikey' => $apiKey],
            'sslverify' => false,
        ]);
        if (is_wp_error($response)) {
            error_log('Enable: Integration Request Error: ' . $response->get_error_message());
            return false;
        }

        $responseBody = wp_remote_retrieve_body($response);
        $responseData = json_decode($responseBody, true);

        return $responseData;
      } catch(Exception $e){
        error_log('Enable: Unexpected Integration Request Error: ' . $e->getMessage());
      }
    }
}
