<?php

class EnablePayment {
    private const GATEWAY_CLASS_FILE = 'class-wc-gateway-enable-payment.php';
    private const GATEWAY_CLASS_NAME = 'WC_Gateway_Enable_Payment';

    public function __construct() {
        $this->registerHooks();
    }

    private function registerHooks(): void {
        add_action('plugins_loaded', [$this, 'initGatewayClass']);
        add_filter('woocommerce_payment_gateways', [$this, 'addGatewayClass']);
//        add_filter('woocommerce_available_payment_gateways', [$this, 'filterAvailablePaymentGateways'], 999);
    }

    public function initGatewayClass(): void {
        $gatewayClassPath = plugin_dir_path(__FILE__) . self::GATEWAY_CLASS_FILE;
        if (file_exists($gatewayClassPath)) {
            require_once $gatewayClassPath;
        } else {
            error_log("Enable: Gateway class file not found: $gatewayClassPath");
        }
    }

    public function addGatewayClass(array $gateways): array {
        $gateways[] = self::GATEWAY_CLASS_NAME;
        return $gateways;
    }

    public function filterAvailablePaymentGateways(array $availableGateways): array {
        if (!isset($availableGateways['enable_payment'])) {
            $availableGateways['enable_payment'] = new WC_Gateway_Enable_Payment();
        }
        $this->logAvailableGateways($availableGateways);
        return $availableGateways;
    }

    private function logAvailableGateways(array $gateways): void {
        error_log('Enable: Available payment gateways: ' . print_r($gateways, true));
    }
}
