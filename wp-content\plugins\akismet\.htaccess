# Only allow direct access to specific Web-available files.

# Apache 2.2
<IfModule !mod_authz_core.c>
	Order Deny,Allow
	Deny from all
</IfModule>

# Apache 2.4
<IfModule mod_authz_core.c>
	Require all denied
</IfModule>

# Akismet CSS and JS
<FilesMatch "^(form\.js|akismet(-frontend|-admin)?\.js|akismet(-admin)?(-rtl)?\.css|inter\.css)$">
	<IfModule !mod_authz_core.c>
		Allow from all
	</IfModule>
	
	<IfModule mod_authz_core.c>
		Require all granted
	</IfModule>
</FilesMatch>

# Akismet images
<FilesMatch "^(logo-a-2x\.png|akismet-refresh-logo\.svg|akismet-refresh-logo@2x\.png|arrow-left\.svg|icon-external\.svg|akismet-activation-banner-elements\.png)$">
	<IfModule !mod_authz_core.c>
		Allow from all
	</IfModule>
	
	<IfModule mod_authz_core.c>
		Require all granted
	</IfModule>
</FilesMatch>
