<?php

use JetBrains\PhpStorm\NoReturn;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

if (!class_exists('WC_Payment_Gateway')) {
    return;
}

class WC_Gateway_Enable_Payment extends WC_Payment_Gateway {
    private const ENABLE_FRONTEND_STAGING_URL = 'https://enable.staging.enable.tech';
    private const ENABLE_FRONTEND_PRODUCTION_URL = 'https://app.enable.tech';

    private bool $payIcon;
    private bool $testMode;
    private string $enableApiKey;
    private bool $debug;
    private string $currency;
    private $logger;

    public function __construct() {
        $this->id = 'enable_payment';
        $this->method_title = 'Enable Payment';
        $this->method_description = 'Custom payment method for Enable plugin';

        // Add support for modern WooCommerce features
        $this->supports = [
            'products',
            'refunds',
            'tokenization'
        ];

        $this->init_form_fields();
        $this->init_settings();
        $this->load_settings();

        add_action('woocommerce_update_options_payment_gateways_' . $this->id, [$this, 'process_admin_options']);
        add_action('woocommerce_api_enable_gateway_response', [$this, 'enable_response_webhook']);

        $this->logger = wc_get_logger();
    }

    private function load_settings(): void {
        $this->title = $this->get_option('title', 'Enable Payment');
        $this->payIcon = 'yes' === $this->get_option('payIcon', 'yes');

        // Fix icon path - use plugin_dir_url instead of plugin_dir_path
        if ($this->payIcon) {
            $this->icon = plugins_url('assets/images/logo.png', __FILE__);
        }

        $this->description = $this->get_option('description', 'Pay securely with Enable Payment Gateway');
        $this->enabled = $this->get_option('enabled', 'no');
        $this->testMode = 'yes' === $this->get_option('testMode', 'yes');

        // Use the main plugin's API key if not set in gateway settings
        $this->enableApiKey = $this->get_option('enableApiKey') ?: get_option('enable_order_api_key', '');

        $this->currency = $this->get_option('currency', 'QAR');
        $this->debug = 'yes' === $this->get_option('debug', 'no');
        $this->order_button_text = __('Place Order with Enable', 'enable-payment');
    }

    public function init_form_fields(): void {
        $this->form_fields = [
            'enabled' => [
                'title' => 'Enable/Disable',
                'label' => 'Enable Enable Payment Gateway',
                'type' => 'checkbox',
                'description' => sprintf(
                    __('Add this return URL under your EButler portal. <b>%s</b>', 'wc-gateway-ebsqr'),
                    site_url('/') . 'wc-api/enable_gateway_response'
                ),
                'default' => 'no'
            ],
            'title' => [
                'title' => 'Title',
                'type' => 'text',
                'description' => 'This controls the title which the user sees during checkout for the main Title.',
                'default' => '',
                'desc_tip' => true,
            ],
            'payIcon' => [
                'title' => 'Show Icon',
                'label' => 'Show/Hide Icon',
                'type' => 'checkbox',
                'description' => '',
                'default' => 'yes',
                'desc_tip' => true,
            ],
            'description' => [
                'title' => 'Description',
                'type' => 'textarea',
                'description' => 'This controls the description which the user sees during checkout.',
                'default' => 'Pay with your credit card via EButler payment gateway.',
            ],
            'testMode' => [
                'title' => 'Testing Mode',
                'label' => 'Testing Mode',
                'type' => 'checkbox',
                'description' => 'Place the payment to Enable Staging ENV instead of the production (API key must exist in staging in that case).',
                'default' => 'yes',
                'desc_tip' => false,
            ],
            'debug' => [
                'title' => 'Debug mode',
                'label' => 'Enable Debug Mode',
                'type' => 'checkbox',
                'description' => 'Print the payment object that will be sent to enable in the console without sending the request',
                'default' => 'yes',
                'desc_tip' => false,
            ],
            'enableApiKey' => [
                'title' => 'Enable API Key',
                'type' => 'text',
                'description' => 'Enter your Enable API key. Leave empty to use the main plugin API key.',
                'default' => get_option('enable_order_api_key', ''),
                'desc_tip' => true,
            ],
            'currency' => [
                'title' => 'Currency',
                'type' => 'text',
                'default' => 'QAR'
            ],
        ];
    }

    public function process_payment($order_id): ?array {
        try {
            $order = wc_get_order($order_id);

            if (!$order) {
                throw new Exception('Invalid order ID: ' . $order_id);
            }

            // Validate API key
            if (empty($this->enableApiKey)) {
                throw new Exception('Enable API key is not configured');
            }

            $enableHelper = new EnableHelper();
            $FRONTEND_URL = $this->testMode ? self::ENABLE_FRONTEND_STAGING_URL : self::ENABLE_FRONTEND_PRODUCTION_URL;

            $requestDetails = [
                'integrationType' => 'payment',
                'paymentData' => $enableHelper->extractPaymentData($order, $this->testMode)
            ];

            if ($this->debug) {
                $this->logger->info('Enable Payment Request: ' . wp_json_encode($requestDetails), ['source' => 'enable-payment']);
            }

            $response = $enableHelper->performIntegrationRequest($requestDetails, $order, $this->enableApiKey, false, $this->debug, $this->testMode);

            if (is_wp_error($response)) {
                throw new Exception('API Request Error: ' . $response->get_error_message());
            }

            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                $error_message = wp_remote_retrieve_response_message($response);
                throw new Exception("API returned error code {$response_code}: {$error_message}");
            }

            $responseBody = wp_remote_retrieve_body($response);
            $responseJson = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON response from Enable API');
            }

            if (!isset($responseJson['data']['code'])) {
                throw new Exception('Payment code not found in API response');
            }

            $paymentCode = $responseJson['data']['code'];

            // Store payment code in order meta
            $order->update_meta_data('EBL_paymentCode', $paymentCode);
            $order->update_meta_data('EBL_payment_method', 'enable_payment');
            $order->update_meta_data('EBL_test_mode', $this->testMode ? 'yes' : 'no');
            $order->save();

            if ($this->debug) {
                $this->logger->info('Enable Payment Code Generated: ' . $paymentCode, ['source' => 'enable-payment']);
            }

            return [
                'result' => 'success',
                'redirect' => $FRONTEND_URL . '/payment/' . $paymentCode
            ];

        } catch (Exception $e) {
            $this->handle_error_response($order ?? null, $e->getMessage());

            // Return to checkout with error
            wc_add_notice('Payment error: ' . $e->getMessage(), 'error');
            return [
                'result' => 'failure',
                'redirect' => wc_get_checkout_url()
            ];
        }
    }

    private function handle_error_response($order, $error_message): void {
        $this->logger->error('Enable Payment Error: ' . $error_message, ['source' => 'enable-payment']);

        if ($order) {
            $order->update_status('failed', 'Enable Payment Error: ' . $error_message);
            $order->add_order_note('Enable Payment failed: ' . $error_message);
        }
    }

    #[NoReturn] public function enable_response_webhook(): void {
        try {
            // Validate required GET parameters
            $paymentCode = sanitize_text_field($_GET['payment_code'] ?? '');
            $responseCode = sanitize_text_field($_GET['responsecode'] ?? '');

            if (empty($paymentCode) || empty($responseCode)) {
                throw new Exception('Missing required parameters: payment_code or responsecode');
            }

            $this->logger->info("Enable webhook received - Payment Code: {$paymentCode}, Response Code: {$responseCode}", ['source' => 'enable-payment']);

            $order = $this->fetchingOrderUsingPaymentCode($paymentCode);

            if (!$order) {
                throw new Exception("Order not found for payment code: {$paymentCode}");
            }

            // Check if payment was successful
            if ($responseCode === '000') {
                $this->process_successful_payment($order, $paymentCode);
                $order->add_order_note("Enable payment completed successfully. Payment Code: {$paymentCode}");
            } else {
                $failureReason = isset($_GET['udf5']) ? sanitize_text_field($_GET['udf5']) : "Payment failed with response code: {$responseCode}";
                $order->update_status('failed', $failureReason);
                $order->add_order_note("Enable payment failed. Response Code: {$responseCode}, Reason: {$failureReason}");
            }

            // Redirect to order received page
            wp_redirect($this->get_return_url($order));

        } catch (Exception $e) {
            $error_message = 'Enable Payment webhook error: ' . $e->getMessage();
            $this->logger->error($error_message, ['source' => 'enable-payment']);

            // Update order status if order exists
            if (isset($order) && $order) {
                $order->update_status('failed', $error_message);
                $order->add_order_note($error_message);
                wp_redirect($this->get_return_url($order));
            } else {
                // Redirect to checkout with error message
                wc_add_notice('Payment processing error. Please try again.', 'error');
                wp_redirect(wc_get_checkout_url());
            }
        }
        exit;
    }

    private function process_successful_payment($order, $paymentCode): void {
        $order->update_meta_data('EBL_paymentCode',$paymentCode);
        $order->payment_complete();
        wc_reduce_stock_levels($order->get_id());
        WC()->cart->empty_cart();
    }

    private function fetchingOrderUsingPaymentCode($paymentCode): ?WC_Order {
        global $wpdb;

        $meta_key = 'EBL_paymentCode';

        // Direct database query to avoid potential conflicts with multilanguage plugins
        $order_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id
            FROM {$wpdb->postmeta}
            WHERE meta_key = %s
            AND meta_value = %s
            ORDER BY post_id DESC
            LIMIT 1",
            $meta_key,
            $paymentCode
        ));

        if (!$order_id) {
            $order_id = $wpdb->get_var($wpdb->prepare(
                "SELECT order_id
                FROM wp_wc_orders_meta
                WHERE meta_key = %s
                AND meta_value = %s
                ORDER BY order_id DESC
                LIMIT 1",
                $meta_key,
                $paymentCode
            ));
        }

        if (!$order_id) {
          $args = array(
            'limit'      => 1, // Retrieve only one order
            'meta_key'   => 'EBL_paymentCode',    // Replace with your meta key
            'meta_value' => $paymentCode,  // Optional: Replace with your meta value if needed
          );

          $orders = wc_get_order($args);

          if ( ! empty( $orders ) ) {
            $order = $orders[0]; // Get the first (and only) order
          } else {
            $this->logger->error("Failed to load order with payment code: $paymentCode", ['source' => 'enable-woocommerce-plugin']);
            return null;
          }

        } else {
          $order = wc_get_order($order_id);
        }

        if (!$order) {
            $this->logger->error("Failed to load order with ID: $order_id", ['source' => 'enable-woocommerce-plugin']);
            return null;
        }

        return $order;
    }
}
