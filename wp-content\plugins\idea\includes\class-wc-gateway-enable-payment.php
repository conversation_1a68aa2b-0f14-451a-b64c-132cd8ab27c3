<?php

use Jet<PERSON>rains\PhpStorm\NoReturn;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

if (!class_exists('WC_Payment_Gateway')) {
    return;
}

class WC_Gateway_Enable_Payment extends WC_Payment_Gateway {
    private const ENABLE_FRONTEND_STAGING_URL = 'https://enable.staging.enable.tech';
    private const ENABLE_FRONTEND_PRODUCTION_URL = 'https://app.enable.tech';

    private bool $payIcon;
    private bool $testMode;
    private string $enableApiKey;
    private bool $debug;
    private string $currency;
    private $logger;

    public function __construct() {
        $this->id = 'enable_payment';
        $this->method_title = 'Enable Payment';
        $this->method_description = 'Custom payment method for Enable plugin';
        $this->supports = ['products'];

        $this->init_form_fields();
        $this->init_settings();
        $this->load_settings();

        add_action('woocommerce_update_options_payment_gateways_' . $this->id, [$this, 'process_admin_options']);
        add_action('woocommerce_api_enable_gateway_response', [$this, 'enable_response_webhook']);

        $this->logger = new WC_Logger();
    }

    private function load_settings(): void {
        $this->title = $this->get_option('title');
        $this->payIcon = 'yes' === $this->get_option('payIcon');
        $this->icon = plugin_dir_path(__FILE__) . 'assets/images/logo.png';
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        $this->testMode = 'yes' === $this->get_option('testMode');
        $this->enableApiKey = $this->get_option('enableApiKey');
        $this->currency = $this->get_option('currency');
        $this->debug = 'yes' === $this->get_option('debug');
        $this->order_button_text = __('Place Order', 'wc-gateway-ebsqr');
    }

    public function init_form_fields(): void {
        $this->form_fields = [
            'enabled' => [
                'title' => 'Enable/Disable',
                'label' => 'Enable Enable Payment Gateway',
                'type' => 'checkbox',
                'description' => sprintf(
                    __('Add this return URL under your EButler portal. <b>%s</b>', 'wc-gateway-ebsqr'),
                    site_url('/') . 'wc-api/enable_gateway_response'
                ),
                'default' => 'no'
            ],
            'title' => [
                'title' => 'Title',
                'type' => 'text',
                'description' => 'This controls the title which the user sees during checkout for the main Title.',
                'default' => '',
                'desc_tip' => true,
            ],
            'payIcon' => [
                'title' => 'Show Icon',
                'label' => 'Show/Hide Icon',
                'type' => 'checkbox',
                'description' => '',
                'default' => 'yes',
                'desc_tip' => true,
            ],
            'description' => [
                'title' => 'Description',
                'type' => 'textarea',
                'description' => 'This controls the description which the user sees during checkout.',
                'default' => 'Pay with your credit card via EButler payment gateway.',
            ],
            'testMode' => [
                'title' => 'Testing Mode',
                'label' => 'Testing Mode',
                'type' => 'checkbox',
                'description' => 'Place the payment to Enable Staging ENV instead of the production (API key must exist in staging in that case).',
                'default' => 'yes',
                'desc_tip' => false,
            ],
            'debug' => [
                'title' => 'Debug mode',
                'label' => 'Enable Debug Mode',
                'type' => 'checkbox',
                'description' => 'Print the payment object that will be sent to enable in the console without sending the request',
                'default' => 'yes',
                'desc_tip' => false,
            ],
            'enableApiKey' => [
                'title' => 'Enable API Key',
                'type' => 'text',
                'default' => $this->get_option('enable_order_api_key')
            ],
            'currency' => [
                'title' => 'Currency',
                'type' => 'text',
                'default' => 'QAR'
            ],
        ];
    }

    public function process_payment($order_id): ?array {
        $order = wc_get_order($order_id);
        $enableHelper = new EnableHelper();
        $FRONTEND_URL = $this->testMode ? self::ENABLE_FRONTEND_STAGING_URL : self::ENABLE_FRONTEND_PRODUCTION_URL;
        $requestDetails = [
            'integrationType' => 'payment',
            'paymentData' => $enableHelper->extractPaymentData($order, $this->testMode)
        ];

        $response = $enableHelper->performIntegrationRequest($requestDetails, $order, $this->enableApiKey, false, $this->debug, $this->testMode);

        if (is_wp_error($response)) {
            $this->handle_error_response($order, $response->get_error_message());
            return $this->get_error_return_array($order);
        }

        if (wp_remote_retrieve_response_code($response) != 200) {
            $this->handle_error_response($order, wp_remote_retrieve_response_message($response));
            return $this->get_error_return_array($order);
        }

        $responseBody = wp_remote_retrieve_body($response);
        $responseJson = json_decode($responseBody);
        $paymentCode = $responseJson->data->code;

        $order->update_meta_data('EBL_paymentCode', $paymentCode);
        $order->save();

        return [
            'result' => 'success',
            'redirect' => $FRONTEND_URL . '/payment/' . $paymentCode
        ];
    }

    private function handle_error_response($order, $error_message): void {
        error_log('Enable: Error: ' . $error_message);
        $order->update_status('failed', $error_message, true);
    }

    private function get_error_return_array($order): array {
        return [
            'result'   => 'success',
            'redirect' => $this->get_return_url($order),
        ];
    }

    #[NoReturn] public function enable_response_webhook(): void {
        try {
            // Validate required GET parameters
            $paymentCode = $_GET['payment_code'] ?? null;
            $responseCode = $_GET['responsecode'] ?? null;

            if (!$paymentCode || !$responseCode) {
                throw new Exception('Missing required parameters');
            }

            $order = $this->fetchingOrderUsingPaymentCode($paymentCode);

            if (!$order) {
                throw new Exception('Invalid order');
            }

            if ($responseCode === '000') {
                $this->process_successful_payment($order, $paymentCode);
            } else {
                $failureReason = isset($_GET['udf5']) ? sanitize_text_field($_GET['udf5']) : 'Payment failed';
                $order->update_status('failed', $failureReason, true);
            }

            wp_redirect($this->get_return_url($order));
        } catch (Exception $e) {
            // Log the error using WooCommerce logger
            $this->logger->error(
                'Payment webhook error: ' . $e->getMessage() . (isset($paymentCode) ? " Payment code: $paymentCode" : ""),
                ['source' => 'enable-woocommerce-plugin']
            );

            // Optionally update order status if order exists
            if (isset($order) && $order) {
                $order->update_status('failed', 'Error processing payment: ' . $e->getMessage() . (isset($paymentCode) ? " Payment code: $paymentCode" : ""), true);
            }

            // Redirect to an error page
            wp_redirect(wc_get_checkout_url());
        }
        exit;
    }

    private function process_successful_payment($order, $paymentCode): void {
        $order->update_meta_data('EBL_paymentCode',$paymentCode);
        $order->payment_complete();
        wc_reduce_stock_levels($order->get_id());
        WC()->cart->empty_cart();
    }

    private function fetchingOrderUsingPaymentCode($paymentCode): ?WC_Order {
        global $wpdb;

        $meta_key = 'EBL_paymentCode';

        // Direct database query to avoid potential conflicts with multilanguage plugins
        $order_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id
            FROM {$wpdb->postmeta}
            WHERE meta_key = %s
            AND meta_value = %s
            ORDER BY post_id DESC
            LIMIT 1",
            $meta_key,
            $paymentCode
        ));

        if (!$order_id) {
            $order_id = $wpdb->get_var($wpdb->prepare(
                "SELECT order_id
                FROM wp_wc_orders_meta
                WHERE meta_key = %s
                AND meta_value = %s
                ORDER BY order_id DESC
                LIMIT 1",
                $meta_key,
                $paymentCode
            ));
        }

        if (!$order_id) {
          $args = array(
            'limit'      => 1, // Retrieve only one order
            'meta_key'   => 'EBL_paymentCode',    // Replace with your meta key
            'meta_value' => $paymentCode,  // Optional: Replace with your meta value if needed
          );

          $orders = wc_get_order($args);

          if ( ! empty( $orders ) ) {
            $order = $orders[0]; // Get the first (and only) order
          } else {
            $this->logger->error("Failed to load order with payment code: $paymentCode", ['source' => 'enable-woocommerce-plugin']);
            return null;
          }

        } else {
          $order = wc_get_order($order_id);
        }

        if (!$order) {
            $this->logger->error("Failed to load order with ID: $order_id", ['source' => 'enable-woocommerce-plugin']);
            return null;
        }

        return $order;
    }
}
