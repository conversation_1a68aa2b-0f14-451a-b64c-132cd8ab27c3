const settings = window.wc.wcSettings.getSetting('enable_payment_data', {});
const label = window.wp.htmlEntities.decodeEntities(settings.title) || 'Enable Payment';

const Content = () => {
    return window.wp.htmlEntities.decodeEntities(settings.description || '');
};

const Block_Gateway = {
    name: 'enable_payment',
    label: label,
    content: Object(window.wp.element.createElement)(Content, null),
    edit: Object(window.wp.element.createElement)(Content, null),
    canMakePayment: () => true,
    ariaLabel: label,
    supports: {
        features: settings.supports,
    },
};

window.wc.wcBlocksRegistry.registerPaymentMethod(Block_Gateway);
